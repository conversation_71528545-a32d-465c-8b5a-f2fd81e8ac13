buildscript {
    ext.kotlin_version = rootProject.ext.has('kotlinVersion') ? rootProject.ext.get('kotlinVersion') : project.properties['Mtr_kotlinVersion']

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

def getExtOrDefault(name) {
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['Mtr_' + name]
}

def getExtOrIntegerDefault(name) {
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['Mtr_' + name]).toInteger()
}

android {
    compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
    buildToolsVersion getExtOrDefault('buildToolsVersion')

    defaultConfig {
        minSdkVersion 26
        targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
        versionCode 1
        versionName "1.0"
        
        // 将ndk配置移到defaultConfig内部
        externalNativeBuild {
            cmake {
                cppFlags ""
                arguments "-DANDROID_STL=c++_shared"
            }
        }
        
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }
    }
    
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
        }
    }
}

repositories {
    google()
    mavenCentral()

    def found = false
    def defaultDir = null
    def androidSourcesName = 'React Native sources'

    if (rootProject.ext.has('reactNativeAndroidRoot')) {
        defaultDir = rootProject.ext.get('reactNativeAndroidRoot')
    } else {
        defaultDir = new File(projectDir, '/../../../node_modules/react-native/android')
    }

    if (defaultDir.exists()) {
        maven {
            url defaultDir.toString()
            name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${defaultDir.canonicalPath}")
        found = true
    } else {
        def parentDir = rootProject.projectDir

        1.upto(5, {
            if (found) return true
            parentDir = parentDir.parentFile

            def androidSourcesDir = new File(parentDir, 'node_modules/react-native')
            def androidPrebuiltBinaryDir = new File(parentDir, 'node_modules/react-native/android')

            if (androidPrebuiltBinaryDir.exists()) {
                maven {
                    url androidPrebuiltBinaryDir.toString()
                    name androidSourcesName
                }

                logger.info(":${project.name}:reactNativeAndroidRoot ${androidPrebuiltBinaryDir.canonicalPath}")
                found = true
            } else if (androidSourcesDir.exists()) {
                maven {
                    url androidSourcesDir.toString()
                    name androidSourcesName
                }

                logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
                found = true
            }
        })
    }

    if (!found) {
        throw new GradleException(
                "${project.name}: unable to locate React Native android sources. " +
                        "Ensure you have you installed React Native as a dependency in your project and try again."
        )
    }
}

dependencies {
    // 添加 Kotlin 协程的核心依赖
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"

    // 其他依赖
    implementation "com.facebook.react:react-native:+"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
}
