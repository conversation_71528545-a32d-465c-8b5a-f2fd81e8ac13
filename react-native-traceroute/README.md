# MoegoTraceroute

## 安装

```sh
npm install @moego/react-native-traceroute
```

## 使用

```js
import { MoegoMtrModule } from '@moego/react-native-traceroute';

//开始诊断
const mtr = new MoegoMtrModule();

mtr.startTraceroute({
  domain: 'nlb-api-mobile.moego.pet',
  timeout: 2,
  maxAttempts: 2,
});

// 监听诊断结果
mtr.addEventListener(TracerouteEvent.Message, (info: string) => {
  console.log(info)
});

// 诊断结束
mtr.addEventListener(TracerouteEvent.End, () => {
  mtr.removeInstance();
});
```
