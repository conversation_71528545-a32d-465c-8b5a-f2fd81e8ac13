import chalk from 'chalk';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import {
  ANDROID_CONFIG_FILE_PATH,
  BUMP_VERSION_TYPES,
  BumpVersionType,
  EXPO_CONFIG,
  EXPO_CONFIG_PATH,
  IOS_EXPO_PLIST_PATH,
  IOS_INFO_PLIST_PATH,
  replaceInfoInConfig,
  setExpoStringProperty,
  setPlistProperty,
} from './utils';

export async function main({
  nativeVersion,
  buildNumber,
  runtimeNumber,
}: {
  nativeVersion?: BumpVersionType;
  buildNumber?: boolean;
  runtimeNumber?: boolean;
}) {
  if (nativeVersion) {
    console.log(chalk.greenBright('Start bump native version...'));
    const nextVersion = await bumpNativeVersion(nativeVersion);
    console.log(chalk.greenBright(`Bump native version to ${nextVersion} successfully!`));
  }
  if (buildNumber) {
    console.log(chalk.greenBright('Start bump build number...'));
    const nextBuildNumber = await bumpBuildNumber();
    console.log(chalk.greenBright(`Bump build number to ${nextBuildNumber} successfully!`));
  }
  if (runtimeNumber) {
    console.log(chalk.greenBright('Start bump runtime number...'));
    const nextRuntimeNumber = await bumpRuntimeNumber();
    console.log(chalk.greenBright(`Bump runtime number to ${nextRuntimeNumber} successfully!`));
  }
}

async function bumpNativeVersion(type: BumpVersionType) {
  const currentVersion = EXPO_CONFIG.version!;
  const nextVersion = currentVersion.replace(/(\d+)\.(\d+)\.(\d+)/, (_match, major, minor, patch) => {
    // bump currentVersion version according to the type
    switch (type) {
      case 'major':
        return `${Number(major) + 1}.0.0`;
      case 'minor':
        return `${major}.${Number(minor) + 1}.0`;
      case 'patch':
        return `${major}.${minor}.${Number(patch) + 1}`;
    }
  });
  const regex = /const NATIVE_VERSION = \'(\d+\.\d+\.\d+)\'/;
  await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
    content.replace(regex, `const NATIVE_VERSION = '${nextVersion}'`),
  );
  await replaceInfoInConfig(ANDROID_CONFIG_FILE_PATH, (content) =>
    content.replace(/versionName=(\d+\.\d+\.\d+)/, `versionName=${nextVersion}`),
  );
  setPlistProperty(IOS_INFO_PLIST_PATH, 'CFBundleShortVersionString', nextVersion);
  return nextVersion;
}

async function bumpBuildNumber() {
  const currentBuildNumber = EXPO_CONFIG.ios?.buildNumber;
  const nextBuildNumber = Number(currentBuildNumber) + 1;
  await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
    content.replace(/const BUILD_NUMBER = (\d+)/, `const BUILD_NUMBER = ${nextBuildNumber}`),
  );
  await replaceInfoInConfig(ANDROID_CONFIG_FILE_PATH, (content) =>
    content.replace(/versionCode=(\d+)/, `versionCode=${nextBuildNumber}`),
  );
  setPlistProperty(IOS_INFO_PLIST_PATH, 'CFBundleVersion', nextBuildNumber.toString());
  return nextBuildNumber;
}

async function bumpRuntimeNumber() {
  const currentRuntimeNumber = EXPO_CONFIG.extra?.RUNTIME_NUMBER;
  const nextRuntimeNumber = Number(currentRuntimeNumber) + 1;
  await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
    content.replace(/const PROD_RUNTIME_NUMBER = (\d+)/, `const PROD_RUNTIME_NUMBER = ${nextRuntimeNumber}`),
  );
  const runtimeVersion = `production-v${nextRuntimeNumber}`;
  await setExpoStringProperty('expo_runtime_version', runtimeVersion);
  setPlistProperty(IOS_EXPO_PLIST_PATH, 'EXUpdatesRuntimeVersion', runtimeVersion);
  return nextRuntimeNumber;
}

const argv = yargs(hideBin(process.argv))
  .options({
    nativeVersion: {
      choices: BUMP_VERSION_TYPES,
      description: 'bump native version - major, minor, patch',
    },
    buildNumber: {
      type: 'boolean',
      description: 'increase build number by 1',
    },
    runtimeNumber: {
      type: 'boolean',
      description: 'increase runtime number by 1',
    },
  })
  .version('0.0.1')
  .help()
  .parseSync();

main(argv);
