#!/usr/bin/env bash

set -e

ENV=$1

# Version args
if [[ "$ENV" == "" ]]; then
  echo "Arguments required!

Usage:
1. Build test packages (on non-production branch): ./build_native.sh test
2. Build production packages (on production branch): ./build_native.sh production"
  exit 1
fi

if [[ "$MOEGO_BUSINESS_UPLOAD_STORE_FILE" == "" ]]; then
  echo "You must set the Android signing environment variables (MOEGO_BUSINESS_UPLOAD_STORE_FILE, MOEGO_BUSINESS_UPLOAD_STORE_PASSWORD, MOEGO_BUSINESS_UPLOAD_KEY_ALIAS, MOEGO_BUSINESS_UPLOAD_KEY_PASSWORD)"
  exit 1
fi

echo "Cleaning up untracked files..."
git clean -xfd

echo "Installing npm dependencies..."
pnpm install --frozen-lockfile

echo "Running pre-assemble task..."
pnpm run native:preassemble --env "$ENV"

echo "Injecting Android signing variables..."
if ! grep -q -E '^MOEGO_BUSINESS_UPLOAD_STORE_FILE=.+' ./android/gradle.properties; then
  echo "MOEGO_BUSINESS_UPLOAD_STORE_FILE=$MOEGO_BUSINESS_UPLOAD_STORE_FILE" >> ./android/gradle.properties
  echo "MOEGO_BUSINESS_UPLOAD_STORE_PASSWORD=$MOEGO_BUSINESS_UPLOAD_STORE_PASSWORD" >> ./android/gradle.properties
  echo "MOEGO_BUSINESS_UPLOAD_KEY_ALIAS=$MOEGO_BUSINESS_UPLOAD_KEY_ALIAS" >> ./android/gradle.properties
  echo "MOEGO_BUSINESS_UPLOAD_KEY_PASSWORD=$MOEGO_BUSINESS_UPLOAD_KEY_PASSWORD" >> ./android/gradle.properties
fi

mkdir build

echo "Building Android apk..."
cd android
./gradlew :app:assembleRelease
cd ..
cp ./android/app/build/outputs/apk/release/app-release.apk ./build
echo "app-release.apk is built and placed in ./build directory!"

echo "Building iOS ipa..."
cd ios
pod install --deployment
# TODO: This doesn't build :(
# It fails with error: No such module 'ExpoModulesCore'. Most of the workarounds googled don't work.
xcodebuild archive -project MoeGo2.xcodeproj -scheme MoeGo2 -archivePath MoeGo2.xcarchive -destination 'generic/platform=iOS'
xcodebuild -exportArchive -archivePath MoeGo2.xcarchive -exportOptionsPlist ExportOptions.plist -exportPath ./MoeGo2.exported
cd ..
cp ./ios/MoeGo2.exported/business.ipa ./build
echo "business.ipa is built and placed in ./build directory!"

echo "Congratulation! All thing is done."
