// TODO：以后修改配置项，暂时忽略。
const config = {
  color: {
    CANCELLED: '#A3ACB7',
    YELLOW: '#FAAD14',
    DANGER_APPT: '#E02020',
    DANGER_DARK: '#B10606',
    DANGER: '#D0021B',
    SUCCESS: '#24B45E',
    DANGER_LIGHT: '#FAE6E8',
    PREMIUM: '#F15A2B',
    ORANGE: '#F96B18',
    BORDER_ORANGE: '#F96B18',
    ORANGE_LIGHT: '#FEE1D1',
    BLUE_APPT: '#5E72E4',
    BLUE: '#0091FF',
    LINK: '#81d4fa',
    BLUE_LIGHT: '#E6F4FF',
    GREEN: '#29CD57',
    GREEN_LIGHT: '#EAFAEE',
    DARK: '#000000',
    BLACK_DARK: '#2A2D34',
    BLACK: '#333333',
    BLA<PERSON>K_LIGHT: '#4A4A4A',
    GRAY_DARK: '#666666',
    GRAY: '#999999',
    GRAY_WHITE: '#C7C7C7',
    BORDER_GRAY: '#DFE0E1',
    E0: '#E0E0E0',
    E6: '#E6E6E6',
    BORDER_LIGHT: '#EEEEEE',
    BORDER_WHITE: '#F0F0F0',
    F1: '#F1F1F1',
    BG_PAGE: '#F3F3F3',
    WHITE_GRAY: '#FAFAFA',
    WHITE: '#FFFFFF',
    C4: '#C4C4C4',
    ASTERISK: '#F54A53',
    CCC: '#CCCCCC',
    PURPLE_DARK: '#9C68FF',
    PURPLE_LIGHT: '#FBF6FF',
    YELLOW_WEAK: '#FFF7E8',
  },
  gaps: [0, 2, 4, 6, 8, 10, 12, 16, 20, 24, 32, 40],
  fontSizes: [28, 24, 20, 18, 16, 14, 12, 10, 9],
  font: {
    REGULAR: 'ProximaNova',
    SEMI_BOLD: 'ProximaNova-Semibold',
    BOLD: 'ProximaNova-Bold',
  },
};

const mGapeScopes = [['margin', 'MG']];
const pGapScopes = [['padding', 'PD']];
const gapDirs = ['top', 'bottom', 'left', 'right', 'horizontal', 'vertical', ''];
const gapValues = [0, 2, 4, 6, 8, 10, 12, 16, 20, 24, 32, 40];

const hSizeTypes = [
  ['MNH', 'minHeight'],
  ['MXH', 'maxHeight'],
  ['H', 'height'],
];
const wSizeTypes = [
  ['MNW', 'minWidth'],
  ['MXW', 'maxWidth'],
  ['W', 'width'],
];
const sizes = [0, 10, 20, 24, 28, 32, 40, 48, 54, 60, 72, 80, 100, 120, 150, 180, 200, 240];

const fonts = [
  ['REGULAR', 'ProximaNova'],
  ['SEMI_BOLD', 'ProximaNova-Semibold'],
  ['BOLD', 'ProximaNova-Bold'],
];
const fontSizes = [28, 24, 20, 18, 16, 14, 12, 10, 9];

const colorOpacities = [100, 5, 10, 15, 40, 50];
/** @type {[string, string, string?][]} */
const colors = [
  ['CANCELLED', '#A3ACB7'],
  ['YELLOW', '#FAAD14'],
  ['DANGER_APPT', '#E02020'],
  ['DANGER_DARK', '#B10606'],
  ['DANGER', '#D0021B'],
  ['SUCCESS', '#24B45E'],
  ['DANGER_LIGHT', '#FAE6E8'],
  ['PREMIUM', '#F15A2B'],
  ['ORANGE', '#F96B18'],
  ['BORDER_ORANGE', '#F96B18'],
  ['ORANGE_LIGHT', '#FEE1D1'],
  ['BLUE_APPT', '#5E72E4'],
  ['BLUE', '#0091FF'],
  ['LINK', '#81d4fa'],
  ['BLUE_LIGHT', '#E6F4FF'],
  ['GREEN', '#29CD57'],
  ['GREEN_LIGHT', '#EAFAEE'],
  ['DARK', '#000000'],
  ['BLACK_DARK', '#2A2D34'],
  ['BLACK', '#333333'],
  ['BLACK_LIGHT', '#4A4A4A'],
  ['GRAY_DARK', '#666666'],
  ['GRAY', '#999999'],
  ['GRAY_WHITE', '#C7C7C7'],
  ['BORDER_GRAY', '#DFE0E1'],
  ['E0', '#E0E0E0'],
  ['E6', '#E6E6E6'],
  ['BORDER_LIGHT', '#EEEEEE'],
  ['BORDER_E6', '#E6E6E6'],
  ['F1', '#F1F1F1'],
  ['F2', '#F2F2F2'],
  ['BG_PAGE', '#F3F3F3'],
  ['WHITE_GRAY', '#FAFAFA'],
  ['WHITE', '#FFFFFF'],
  ['C4', '#C4C4C4'],
  ['ASTERISK', '#F54A53'],
  ['CCC', '#CCCCCC'],
  ['NEUTRAL_FILLED', '#F9F9F9'],
  ['NEUTRAL_PEACH', '#FEF0E8'],
  ['BORDER_WHITE', '#F0F0F0'],
  ['NEUTRAL_BACKGROUND', '#F7F8FA'],
  ['PURPLE_DARK', '#9C68FF'],
  ['PURPLE_LIGHT', '#FBF6FF'],
  ['YELLOW_WEAK', '#FFF7E8'],
  ['BORDER_BUTTON', '#CDCDCD', 'BD'],
  ['BG_WARNING_SUBTLE', '#FFF8E5', 'BG'],
  ['BG_WARNING_MILD', '#FFD166', 'BG'],
  ['BG_WARNING_BOLD', '#FAAD14', 'BG'],
  ['TEXT_PRIMARY', '#202020', 'TEXT'],
  ['TEXT_INFORMATION', '#0089FF', 'TEXT'],
  ['TEXT_BRAND', '#F96B18', 'TEXT'],
  ['TEXT_DISABLED', '#B4B4B4', 'TEXT'],
  ['TEXT_DISCOVER', '#9C68FF', 'TEXT'],
  ['ICON_SUCCESS', '#07AB4C', 'TEXT'],
  ['BD_DIVIDER', '#E6E6E6', 'BD'],
];

const seen = new Set();
for (const [c] of colors) {
  if (seen.has(c)) {
    throw new Error(`duplicated color name ${c}`);
  }
  seen.add(c);
}

module.exports = {
  gapValues,
  gapDirs,
  mGapeScopes,
  pGapScopes,
  colorOpacities,
  fontSizes,
  colors,
  fonts,
  sizes,
  wSizeTypes,
  hSizeTypes,
  config,
};
