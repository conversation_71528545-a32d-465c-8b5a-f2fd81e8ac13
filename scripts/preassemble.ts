import chalk from 'chalk';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { updatesPatchManager } from './toggle_updates_patch';
import {
  ANDROID_CONFIG_FILE_PATH,
  EXPO_CONFIG_PATH,
  IOS_EXPO_PLIST_PATH,
  getBranch,
  replaceInfoInConfig,
  setExpoStringProperty,
  setPlistProperty,
} from './utils';
const ENV_LIST = ['test', 'production'] as const;
const TEST_ENV = 'TEST';
type Env = (typeof ENV_LIST)[number];
const IOS_ENTRY_FILE_PATH = './ios/MoeGo2/AppDelegate.mm';
const ANDROID_ENTRY_FILE_PATH = './android/app/src/main/java/com/moement/moego/business/MainApplication.kt';
const SQUARE_READER_ARM64_INFO_PLIST_PATH =
  'ios/SquareReaderSDK.xcframework/ios-arm64/SquareReaderSDK.framework/Info.plist';
const SQUARE_READER_X86_INFO_PLIST_PATH =
  'ios/SquareReaderSDK.xcframework/ios-arm64_x86_64-simulator/SquareReaderSDK.framework/Info.plist';

async function main({ env }: { env: Env }) {
  await replaceEnvInConfigFile(env);
  await replaceExpoUpdatesConfig(env);
  await replaceBranchName(env);
  await replaceIntercomKeyAndId(env);
  await replaceSquareReaderId(env);
  await replaceIOSRegistrationConfig(env);
  // await toggleUpdatesPatch(env);
}

async function replaceEnvInConfigFile(env: Env) {
  if (env === 'test') {
    // replace DEFAULT_CI_ACTION with 'LOCALLY_TEST_BUILD'
    console.log(chalk.greenBright('Start replace DEFAULT_CI_ACTION with LOCALLY_TEST_BUILD...'));
    await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
      content.replace(
        /const DEFAULT_CI_ACTION = 'LOCALLY_PROD_BUILD'/,
        `const DEFAULT_CI_ACTION = 'LOCALLY_TEST_BUILD'`,
      ),
    );
    setPlistProperty(IOS_EXPO_PLIST_PATH, 'EXUpdatesRuntimeVersion', TEST_ENV);
    await setExpoStringProperty('expo_runtime_version', TEST_ENV);
    console.log(chalk.greenBright('Replace DEFAULT_CI_ACTION with LOCALLY_TEST_BUILD successfully!'));
  }
}

async function replaceExpoUpdatesConfig(env: Env) {
  if (env === 'test') {
    console.log(chalk.greenBright('Start replace expo-updates url and launch config...'));
    const testUpdatesUrl = 'https://expo-updates.t2.moego.dev/api/manifest';
    setPlistProperty(IOS_EXPO_PLIST_PATH, 'EXUpdatesURL', testUpdatesUrl);
    await setExpoStringProperty('expo_updates_url', testUpdatesUrl);
    // update checkAutomatically to NEVER
    await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
      content.replace(/const UPDATES_CHECK_AUTOMATICALLY = 'ALWAYS'/, `const UPDATES_CHECK_AUTOMATICALLY = 'NEVER'`),
    );
    await setExpoStringProperty('expo_updates_check_on_launch', 'NEVER');
    setPlistProperty(IOS_EXPO_PLIST_PATH, 'EXUpdatesCheckOnLaunch', 'NEVER');
    console.log(chalk.greenBright('Replace expo-updates url and launch config successfully!'));
  }
}

async function replaceBranchName(env: Env) {
  if (env === 'test') {
    console.log(chalk.greenBright('Start replace branch name...'));
    const nextBranchName = getBranch();
    await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
      content.replace(/const DEFAULT_BRANCH_NAME = 'production'/, `const DEFAULT_BRANCH_NAME = '${nextBranchName}'`),
    );
    console.log(chalk.greenBright('Replace branch name successfully!'));
  }
}
async function replaceIntercomKeyAndId(env: Env) {
  if (env === 'test') {
    // 这里不修改 ExpoConfig 内的配置，因为不会被直接用到，除非接入 managed workflow。
    console.log(chalk.greenBright('Start replace intercom key...'));
    await replaceInfoInConfig(IOS_ENTRY_FILE_PATH, (content) =>
      content.replace(
        `[IntercomModule initialize:@"ios_sdk-0a32dacbbea1f90edf9b01acead2ed9afde68f89" withAppId:@"oh5g31xm"]`,
        `[IntercomModule initialize:@"ios_sdk-adfec30f1be13c0be34d819182bb074889e9538e" withAppId:@"g7ncob2y"]`,
      ),
    );
    await replaceInfoInConfig(ANDROID_ENTRY_FILE_PATH, (content) =>
      content.replace(
        `IntercomModule.initialize(this, "android_sdk-f3e399e7548e930ec9813f5bb31abbb9a8198680", "oh5g31xm")`,
        `IntercomModule.initialize(this, "android_sdk-b396942a96403d23681217b0658b0a46dff4f52f", "g7ncob2y")`,
      ),
    );
    console.log(chalk.greenBright('Replace intercom key successfully!'));
  }
}

async function replaceSquareReaderId(env: Env) {
  if (env === 'test') {
    console.log(chalk.greenBright('Start replace square reader id...'));
    await replaceInfoInConfig(ANDROID_CONFIG_FILE_PATH, (content) =>
      content.replace(
        /SQUARE_READER_SDK_APPLICATION_ID=sq0idp-ojCjvDy12EX07-dcKOVf9g/,
        `SQUARE_READER_SDK_APPLICATION_ID=sq0idp-nZSpet2hDgW2nXvem_vXyg`,
      ),
    );
    await replaceInfoInConfig(ANDROID_CONFIG_FILE_PATH, (content) =>
      content.replace(
        /SQUARE_READER_SDK_REPOSITORY_PASSWORD=swy6kn5vynjazs6zcr7k2jbkhu6civ6xthgun2oaglfsz4v4mr3q/,
        `SQUARE_READER_SDK_REPOSITORY_PASSWORD=xlnx4bjscmfpir4jbugrmxwos5n665avo3ry6bfuu2fcxxn3fhiq`,
      ),
    );
    setPlistProperty(SQUARE_READER_ARM64_INFO_PLIST_PATH, 'SDKApplicationID', 'sq0idp-nZSpet2hDgW2nXvem_vXyg');
    setPlistProperty(SQUARE_READER_X86_INFO_PLIST_PATH, 'SDKApplicationID', 'sq0idp-nZSpet2hDgW2nXvem_vXyg');
    console.log(chalk.greenBright('Replace square reader id successfully!'));
  }
}

async function replaceIOSRegistrationConfig(env: Env) {
  if (env === 'production') {
    console.log(chalk.greenBright('Start replace ios registration config...'));
    await replaceInfoInConfig(EXPO_CONFIG_PATH, (content) =>
      content.replace(/const DISABLE_IOS_REGISTRATION = false/, `const DISABLE_IOS_REGISTRATION = true`),
    );
    console.log(chalk.greenBright('Replace ios registration config successfully!'));
  }
}

async function toggleUpdatesPatch(env: Env) {
  if (env === 'production') {
    updatesPatchManager.togglePatch(true);
  } else {
    updatesPatchManager.togglePatch(false);
  }
}

const argv = yargs(hideBin(process.argv))
  .options({
    env: {
      choices: ENV_LIST,
      description: 'set env',
      required: true,
    },
  })
  .version('0.0.1')
  .help()
  .parseSync();

main(argv);
