/*
 * @since 2020-08-19 18:46:29
 * <AUTHOR> <<EMAIL>>
 */

const fs = require('fs-extra');
const path = require('path');
const cp = require('child_process');
const yargs = require('yargs');
const svgo = require('svgo');
const { replaceAttrBySelector, USE_CURRENT_COLOR } = require('./svgo-plugins');

const argv = yargs
  .options({
    basedir: {
      type: 'string',
      default: './src/assets/icon',
      description: '要生成 .d.ts 的 icon 所在目录',
    },
    svg: {
      type: 'boolean',
      default: true,
      description: '将 svg 声明成 Svg 组件',
    },
    prefix: {
      type: 'string',
      default: 'Icon',
      description: '生成的变量名称的前缀',
    },
    preview: {
      type: 'boolean',
      default: false,
      description: '是否生成预览文件 preview.html',
    },
    open: {
      type: 'boolean',
      default: false,
      description: '是否在浏览器中打开预览文件',
    },
  })
  .strict().argv;

argv.basedir = path.resolve(argv.basedir);
// const svgo = new SVGO({
//   // plugins: [{ removeDimensions: true }, { replaceAttrBySelector: replaceAttrBySelector(USE_CURRENT_COLOR) }],
// });

async function main() {
  const files = await fs.readdir(argv.basedir);
  const items = [];
  await Promise.all(
    files
      .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
      .map(async (file, i) => {
        if (/\.(?:svg|png|jpe?g|gif|bmp|tiff|otf)$/.test(file)) {
          if (/@[234]x\.[^.]+$/.test(file)) {
            return;
          }
          const name = argv.prefix + file.replace(/(?:^|-|_|\.|@)+(.)/g, (_, $1) => $1.toUpperCase());
          const isSvg = argv.svg && /\.svg$/.test(file);
          if (isSvg) {
            const content = await fs.readFile(path.join(argv.basedir, file), 'utf8');
            const svg = await svgo.optimize(content);
            items[i] = `<div class="icon"><div class="svg">${svg.data}</div><div>${name}</div></div>`;
          } else {
            items[i] = `<div class="icon"><img src="./${file}" alt="${file}">${name}</div>`;
          }
          await fs.writeFile(
            `${argv.basedir}/${file}.d.ts`,
            isSvg
              ? [
                  "import { FC } from 'react';",
                  "import { SvgProps } from 'react-native-svg';",
                  '',
                  `declare const ${name}: FC<SvgProps>;`,
                  `export default ${name};`,
                  '',
                ].join('\n')
              : [`declare const ${name}: number;`, `export default ${name};`, ''].join('\n'),
          );
        }
      }),
  );
  argv.preview &&
    (await fs.writeFile(
      `${argv.basedir}/preview.html`,
      `<!doctype html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="initial-width=device-width">
<title>MoeGo Icons</title>
<style>
.content {
  max-width: 1050px;
  margin: 0 auto;
  text-align: center;
}

.icons.orange {
  color: orange;
}

.icons.dark {
  background: #e0e0e0;
}

.icon {
  display: inline-block;
  text-align: center;
  margin: 25px;
  width: 100px;
  word-break: break-all;
  vertical-align: top;
}

.icon img {
  display: block;
  width: 50px;
  height: 50px;
  margin: 0 auto 12px;
}
</style>
</head>
<body>
<div class="content">
<h1>MoeGo Icons</h1>
<label><input type="checkbox" onchange="setOrange(event)"> Orange</label>
<label><input type="checkbox" onchange="setDark(event)"> Dark</label>
<div class="icons">
${items.join('')}
</div>
</div>
<script>
function setDark(e) {
  document.querySelector('.icons').classList.toggle('dark', e.target.checked)
}
function setOrange(e) {
  document.querySelector('.icons').classList.toggle('orange', e.target.checked)
}
</script>
</body>
</html>
`,
    ));
}

main().then(
  () => {
    console.log('Done!');
    if (argv.open) {
      cp.execSync(`open ${argv.basedir}/preview.html`);
    }
  },
  (e) => {
    console.error(e);
  },
);
