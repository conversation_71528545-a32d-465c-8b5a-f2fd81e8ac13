import chalk from 'chalk';
import path from 'path';
import { getCommitShortHash, replaceInfoInConfig } from './utils';

const SRC_DIR = path.resolve(__dirname, '../src');
const MOE_VERSION_FILE_PATH = path.resolve(SRC_DIR, 'utils/moe-version.ts');

async function replaceMoeVersion() {
  console.log(chalk.greenBright('Start replace moe version...'));
  const version = process.env.BRANCH_NAME + ':' + getCommitShortHash();
  await replaceInfoInConfig(MOE_VERSION_FILE_PATH, (content) => content.replace(/__PLACEHOLDER__/, version.trim()));
  console.log(chalk.greenBright('Replace moe version successfully!'));
}

function replaceEnv() {
  replaceMoeVersion();
}

replaceEnv();
