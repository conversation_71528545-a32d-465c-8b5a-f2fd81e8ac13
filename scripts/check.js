/*
 * @since 2021-10-11 19:17:54
 * <AUTHOR> <<EMAIL>>
 */

const yargs = require('yargs');
const cp = require('child_process');

function debug() {
  if (process.env.DEBUG) {
    console.log.apply(console, arguments);
  }
}

function error(msg) {
  console.error('\u001b[31mERROR: ' + msg + '\u001b[0m');
}

/**
 * @param cmd
 * @return {string}
 */
function exec(cmd) {
  debug('$ ' + cmd);
  return cp.execSync(cmd, { encoding: 'utf-8', maxBuffer: 1 << 25 });
}

async function checkImports() {
  const { dependencies: deps, devDependencies: devDeps } = require('../package.json');
  const unused = new Set(Object.keys(deps));
  unused.delete('tslib');
  unused.delete('react-native-screens');
  unused.delete('react-native-unimodules');
  const match = exec('git grep -n -E "from [\'\\"][^\'\\"]+[\'\\"];$"');
  let matched = false;
  for (const line of match.trim().split('\n')) {
    let module = line.split(/['"]/g)[1];
    if (module.startsWith('./') || module.startsWith('../')) {
      continue;
    }
    if (module.startsWith('@')) {
      module = module.split('/', 2).join('/');
    } else {
      module = module.split('/').shift();
    }
    unused.delete(module);
    if (!deps.hasOwnProperty(module)) {
      if (!matched) {
        error('import module does not exist in package.json:');
        matched = true;
      }
      console.log(line);
    }
  }
  for (const item of unused) {
    if (item.startsWith('@types/')) {
      let name = item.split('/')[1];
      if (name.indexOf('__') > 0) {
        name = '@' + name.replace('__', '/');
      }
      if (deps.hasOwnProperty(name) && !unused.has(name)) {
        unused.delete(item);
      }
    }
  }
  if (unused.size > 0) {
    error('unused modules: ' + Array.from(unused).join(', '));
  }
  for (const k in devDeps) {
    if (deps[k]) {
      error('duplicated dependency: ' + k);
    }
  }
  process.exit(matched || unused.size ? 1 : 0);
}

async function checkTsIgnore() {}

async function checkTypeScript() {}

async function checkCircularDependencies() {}

async function checkPrettier() {}

async function checkNativeModules() {}

async function checkMoment() {}

yargs
  .command('imports', '依赖检查', checkImports)
  .command('ts-ignore', '类型安全检查', checkTsIgnore)
  .command('typescript', '类型检查', checkTypeScript)
  .command('circular-dependencies', '循环依赖检查', checkCircularDependencies)
  .command('prettier', '代码样式检查', checkPrettier)
  .command('native-modules', '原生模块依赖检查', checkNativeModules)
  .command('moment', 'moment 使用检查', checkMoment)
  .demandCommand()
  .strict()
  .help().argv;
