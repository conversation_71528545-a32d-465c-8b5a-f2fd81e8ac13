import { getConfig } from '@expo/config';
import childProcess, { execSync } from 'child_process';
import crypto, { BinaryToTextEncoding } from 'crypto';
import fs from 'fs-extra';
import os from 'os';
import path from 'path';

export const BUMP_VERSION_TYPES = ['major', 'minor', 'patch'] as const;
export type BumpVersionType = (typeof BUMP_VERSION_TYPES)[number];
export const EXPO_CONFIG_PATH = 'app.config.ts';
export const ANDROID_CONFIG_FILE_PATH = 'android/gradle.properties';
export const ANDROID_EXPO_STRING_PATH = 'android/app/src/main/res/values/strings.xml';
export const IOS_INFO_PLIST_PATH = 'ios/MoeGo2/Info.plist';
export const IOS_EXPO_PLIST_PATH = 'ios/MoeGo2/Supporting/Expo.plist';

export async function replaceInfoInConfig(filePath: string, fn: (content: string) => string) {
  const fileContent = await fs.readFile(filePath, 'utf-8');
  const nextFileContent = fn(fileContent);
  await fs.writeFile(filePath, nextFileContent);
}

export function setPlistProperty(filePath: string, key: string, value: string) {
  if (os.platform() === 'darwin') {
    childProcess.execSync(`/usr/libexec/PlistBuddy -c "Set :${key} ${value}" ${filePath}`);
  }
}

// set key and value in expo string file
export async function setExpoStringProperty(key: string, value: string) {
  const fileContent = await fs.readFile(ANDROID_EXPO_STRING_PATH, 'utf-8');
  const nextFileContent = fileContent.replace(
    new RegExp(`<string name="${key}">.*</string>`),
    `<string name="${key}">${value}</string>`,
  );
  await fs.writeFile(ANDROID_EXPO_STRING_PATH, nextFileContent);
}

export function createHash(file: Buffer, hashingAlgorithm: string, encoding: BinaryToTextEncoding) {
  return crypto.createHash(hashingAlgorithm).update(file).digest(encoding);
}

export function convertSHA256HashToUUID(value: string) {
  return `${value.slice(0, 8)}-${value.slice(8, 12)}-${value.slice(12, 16)}-${value.slice(16, 20)}-${value.slice(
    20,
    32,
  )}`;
}

export function getBase64URLEncoding(base64EncodedString: string): string {
  return base64EncodedString.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

export const getExpoConfig = () => {
  const { exp } = getConfig(path.resolve(__dirname, '../'), {
    isPublicConfig: true,
    skipSDKVersionRequirement: true,
  });
  return exp;
};

export const getBranch = () => {
  const branchWithReturn = execSync('git branch --show-current').toString();
  return branchWithReturn.slice(0, -1);
};

export const getCommitShortHash = () => {
  return execSync('git rev-parse --short HEAD').toString().trim();
};

export const EXPO_CONFIG = getExpoConfig();
