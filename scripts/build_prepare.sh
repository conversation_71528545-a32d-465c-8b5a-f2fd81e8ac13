#!/usr/bin/env bash
# @since 2021-04-12 10:41:54
# <AUTHOR> <<EMAIL>>
#
# Works:
# 1. 设置热更新 release channel (android/ios)
#    使用当前代码分支, 如果是 feature/bugfix 或者 production 分支指定测试环境 test, 则使用分支名, 否则使用 production-channelVersion
# 2. 设置版本号 (ios)
#    使用 app.json 中的版本号, 安卓动态获取
# 3. 设置构建版本号 (ios)
#    使用 app.json 中的构建版本号, 安卓动态获取
# 4. 重置热更新版本号
#    重置为空字符串, 表示资源不是热更新得到的
# 5. 如果非生产构建，会将测试环境的一些配置(square/intercom)进行同步
#    TODO: 考虑使用构建中的变量来进行配置

set -xeuo pipefail

ROOT_DIR="$(dirname $0)/.."
FILE_APP_JSON="$ROOT_DIR/app.json"
FILE_IOS_PLIST="$ROOT_DIR/ios/MoeGo2/Info.plist"
FILE_EXPO_PLIST="$ROOT_DIR/ios/MoeGo2/Supporting/Expo.plist"
FILE_ANDROID_MANIFEST="$ROOT_DIR/android/app/src/main/AndroidManifest.xml"
ENV=${1:-production}

__BRANCH__=$(git branch --show-current)
__IOS_RELEASE_CHANNEL_VERSION__=$(jq -r ".expo.extra.RELEASE_CHANNEL_VERSION" "$FILE_APP_JSON")
__ANDROID_RELEASE_CHANNEL_VERSION__=$(jq -r ".expo.extra.RELEASE_CHANNEL_VERSION" "$FILE_APP_JSON")
__VERSION__=$(jq -r ".expo.version" "$FILE_APP_JSON")
__BUILD_NUMBER__=$(jq -r ".expo.ios.buildNumber" "$FILE_APP_JSON")

case $__BRANCH__ in
    production | next)
        case $ENV in
            test)
                __IOS_RELEASE_CHANNEL__="$__BRANCH__"
                __ANDROID_RELEASE_CHANNEL__="$__BRANCH__"
                ;;
            *)
                __IOS_RELEASE_CHANNEL__="$__BRANCH__-v$__IOS_RELEASE_CHANNEL_VERSION__"
                __ANDROID_RELEASE_CHANNEL__="$__BRANCH__-v$__ANDROID_RELEASE_CHANNEL_VERSION__"
                ;;
        esac
      ;;
    feature-* | bugfix-* | test-*)
        __IOS_RELEASE_CHANNEL__="$__BRANCH__"
        __ANDROID_RELEASE_CHANNEL__="$__BRANCH__"
        ;;
    *)
        echo "ERROR: please build on the following branches: production/next/feature-*/bugfix-*"
        exit 1
        ;;
esac

/usr/libexec/PlistBuddy -c \
  "Set :EXUpdatesReleaseChannel \"$__IOS_RELEASE_CHANNEL__\"" \
  "$FILE_EXPO_PLIST"

/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString $__VERSION__" "$FILE_IOS_PLIST"
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion $__BUILD_NUMBER__" "$FILE_IOS_PLIST"

node -p -e "var fs = require('fs');
  var content = fs.readFileSync('$FILE_ANDROID_MANIFEST', 'utf8').replace(
  /(android:name=\"expo.modules.updates.EXPO_RELEASE_CHANNEL\"[\\n ]+android:value=)\"[^\"]+\"/, '\$1\"$__ANDROID_RELEASE_CHANNEL__\"');
  fs.writeFileSync('$FILE_ANDROID_MANIFEST', content);
"


# set as empty string to hide it
jq '.expo.extra.BUILD_ID = ""' "$FILE_APP_JSON" > "$FILE_APP_JSON.tmp"
mv "$FILE_APP_JSON.tmp" "$FILE_APP_JSON"

if [[ "$(git branch --show-current)" != "production" ]] || [[ "$ENV" == "test" ]]; then
  git cherry-pick -n commit-square-sdk-1.6.2-staging
  git cherry-pick -n commit-intercom-sdk-staging
fi
