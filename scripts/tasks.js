/*
 * @since 2021-03-05 14:52:18
 * <AUTHOR> <<EMAIL>>
 */

const yargs = require('yargs');
const fs = require('fs-extra');
const cp = require('child_process');
const path = require('path');
const G = require('glob');
const { generateStyle } = require('./generate_style.js');
// const Axios = require('axios');

/**
 * @param {string} file
 * @return {string}
 */
function readFile(file) {
  return fs.readFileSync(file, { encoding: 'utf-8' });
}

function isScreen(file) {
  const match = file.match(/src\/modules\/([^\/]+)\/([^\/]+)\/([^\/]+)\.tsx$/);
  return match && !['store', 'components'].includes(match[2]);
}

const transformScreen = (files) => {
  files.forEach((file) => {
    if (!isScreen(file)) {
      return;
    }
    let isInScreen = false;
    let isInRender = false;
    let screenStart = -1;
    let screenEnd = -1;
    let renderStart = -1;
    let renderEnd = -1;
    const originalContent = readFile(file);
    const page = path.basename(file, '.tsx');
    const route = `path${page}`.replace(/([A-Z])/g, '_$1').toUpperCase();
    const rawContent = originalContent.replace(
      new RegExp(`\n(export )?class ${page} extends React[^\n]+|\n(export )?const ${page} = memo[^\n]+`),
      `\nexport const ${page} = createLazyStackScreen(${route}, 'TODO', ({}, navigation, route) => {`,
    );
    /** @type {string[]} */
    let content = rawContent.split('\n').map((line, index) => {
      if (/^export const \w+ = create\w+Screen/.test(line)) {
        isInScreen = true;
        screenStart = index;
        return line;
      }
      if (!isInScreen) {
        return line;
      }
      line = line.replace(/this\./g, '').replace(/^ {2}(public |private )?static /, '  ');
      if (isInScreen && line === '}') {
        isInScreen = false;
        screenEnd = index;
        return '})';
      }
      if (isInScreen && /^(\)|}\))/.test(line)) {
        isInScreen = false;
        screenEnd = index;
        return line;
      }
      if (/^ {2}render\(/.test(line)) {
        isInRender = true;
        renderStart = index;
        return '';
      }
      if (isInRender && line === '  }') {
        isInRender = false;
        renderEnd = index;
        return '';
      }
      if (/^ {2}(private )?(async )?(\* ?)?(\w+)\(.*\)(:.*)? {$/.test(line)) {
        return line.replace(/^ {2}(private )?(async )?(\* ?)?/, '  $2function $3');
      }
      if (/^ {2}(private )?(\w+) =/.test(line)) {
        return line.replace(/^ {2}(private )?/, '  const ');
      }
      if (/^ {2}(private )?(\w+)[?!]?:/.test(line)) {
        return line.replace(/^ {2}(private )?(\w+)[?:]?:/, '  const $2:');
      }
      return line;
    });
    if (screenStart > -1 && renderStart > -1) {
      content = content
        .slice(0, renderStart)
        .concat(content.slice(renderEnd, screenEnd), content.slice(renderStart, renderEnd), content.slice(screenEnd));
    }
    if (content.join('\n') === originalContent) {
      return;
    }
    console.log('MATCH SCREEN: %s', file);
    fs.writeFileSync(file, content.join('\n'));
  });
};

const transformClassComponent = (files) => {
  files.forEach((file) => {
    if (isScreen(file)) {
      return;
    }
    let isInScreen = false;
    let isInRender = false;
    let screenStart = -1;
    let screenEnd = -1;
    let renderStart = -1;
    let renderEnd = -1;
    let enterLineMatch;
    const originalContent = readFile(file);
    let content = originalContent
      .replace(/;\n{2,}import/g, ';\nimport')
      .replace(/\/\/ *import[^\n]+\n/, '')
      .split('\n')
      .map((line, index) => {
        if (/export default \w+;/.test(line)) {
          enterLineMatch = void 0;
          return line;
        }
        // if (/^import (\w+).* from '\./.test(line) &&
        // !/\/(assets|model|redux|res)\//.test(line)) { return line.replace(/^import (\w+)(?:,
        // {([^}]+)})? /, 'import { $1, $2 } '); }
        enterLineMatch = line.match(/^(export (default )?)?class (\w+) extends React\.(?:Pure)?Component/);
        if (enterLineMatch) {
          if (screenEnd > -1) {
            console.warn('One more class: %s -> %s', file, line);
            return line;
          }
          isInScreen = true;
          screenStart = index;
          return [
            "import { memo } from 'react';",
            '',
            `export interface ${enterLineMatch[3]}Props {`,
            '}',
            '',
            `${enterLineMatch[2] ? '' : enterLineMatch[1] || ''}const ${enterLineMatch[3]} = memo<${
              enterLineMatch[3]
            }Props>((props) => {`,
          ].join('\n');
        }
        if (!isInScreen && screenEnd > -1) {
          return line;
        }
        if (!isInScreen) {
          return line;
        }
        line = line.replace(/this\./g, '').replace(/^ {2}(public |private )?static /, '  ');
        if (line === '}') {
          isInScreen = false;
          screenEnd = index;
          return '})';
        }
        if (/^(\)|}\))/.test(line)) {
          isInScreen = false;
          screenEnd = index;
          return line;
        }
        if (/^ {2}render\(/.test(line)) {
          isInRender = true;
          renderStart = index;
          return '';
        }
        if (isInRender && line === '  }') {
          isInRender = false;
          renderEnd = index;
          return '';
        }
        if (/^ {2}(private |public )?(async )?(\* ?)?(\w+)\(.*\)(:.*)? {$/.test(line)) {
          return line.replace(/^ {2}(private |public )?(async )?(\* ?)?/, '  $2function $3');
        }
        if (/^ {2}(private |public )?(\w+) =/.test(line)) {
          return line.replace(/^ {2}(private |public )?/, '  const ');
        }
        if (/^ {2}(private |public )?(\w+)[?!]?:/.test(line)) {
          return line.replace(/^ {2}(private |public )?(\w+)[?:]?:/, '  const $2:');
        }
        return line;
      });
    if (enterLineMatch?.[2]) {
      content = content.push(`export default ${enterLineMatch[3]};`, '');
    }
    if (screenStart > -1 && renderStart > -1) {
      content = content
        .slice(0, renderStart)
        .concat(content.slice(renderEnd, screenEnd), content.slice(renderStart, renderEnd), content.slice(screenEnd));
    }
    if (content.join('\n') === originalContent) {
      return;
    }
    console.log('MATCH CC: %s', file);
    fs.writeFileSync(file, content.join('\n'));
  });
};

const moveStyleSheet = (files) => {
  files.forEach((file) => {
    if (/[.\/]styles.ts$/.test(file)) {
      return;
    }
    const content = readFile(file);
    const styleContent = [];
    let inStyle = false;
    const styleNames = [];
    const styleImports = [`import { StyleSheet } from 'react-native';`];
    const newContent = content.split('\n').filter((line, index) => {
      if (/^(export )?const (\w+) = StyleSheet.create\({$/.test(line)) {
        inStyle = true;
        styleNames.push(line.match(/const (\w+) = /)[1]);
        styleContent.push(line.startsWith('export ') ? line : 'export ' + line);
        return false;
      }
      if (inStyle) {
        styleContent.push(line);
        if (line === '});') {
          inStyle = false;
        }
        return false;
      }
      if (/^import .+\/values\/const/.test(line)) {
        styleImports.push(line);
      }
      return true;
    });
    if (styleContent.length === 0) {
      return;
    }
    let lastImport = 0;
    newContent.forEach((line, index) => {
      if (/^import /.test(line)) {
        lastImport = index;
      }
    });
    const base = path.basename(file, '.tsx');
    const styleFile = path.join(path.dirname(file), `${base}.styles.ts`);
    if (fs.existsSync(styleFile)) {
      throw new Error(`SS: ${styleFile} exists already!`);
    }
    console.log('MATCH SS: %s', file);
    styleImports.push('');
    fs.writeFileSync(styleFile, styleImports.concat(styleContent).join('\n') + '\n');
    newContent.splice(lastImport, 0, `import { ${styleNames.join(', ')} } from './${base}.styles';`);
    fs.writeFileSync(file, newContent.join('\n'));
  });
};

const generateScreens = async () => {
  const screenImports = new Map([['../../utils/createScreen', new Set(['StackScreen', 'DrawerScreen'])]]);
  const drawerScreens = new Set();
  const stackScreens = new Set();
  cp.execSync("grep -E '(\\w+) = create(LazyStack|LazyDrawer)Screen' -o -r ./src/modules", { encoding: 'utf8' })
    .trim()
    .split('\n')
    .forEach((l) => {
      const [file, name, , type] = l.split(/[: ]/g);
      if (type === 'createLazyStackScreen') {
        stackScreens.add(name);
      } else {
        drawerScreens.add(name);
      }
      const id = file.replace('./src/', '../../').replace(/\.tsx?$/, '');
      if (!screenImports.has(id)) {
        screenImports.set(id, new Set([name]));
      } else {
        screenImports.get(id).add(name);
      }
    });
  fs.outputFileSync(
    './src/entry/generated/screens.ts',
    [
      `/* This file is generated by ${path.relative(process.cwd(), __filename)}, please do not edit it. */`,
      '',
      '// #region import screens',
      ...Array.from(screenImports.entries())
        .sort(([a], [b]) => a.localeCompare(b))
        .slice(0, -1)
        .map(([id, names]) => `import ${Array.from(names).sort().join(', ')} from '${id}.route';`),
      '// #endregion',
      '',
      `import { DrawerScreen, LazyDrawerRoute, LazyStackRoute, StackScreen } from '../../utils/createScreen';`,
      '',
      'export const DrawerScreenList: (DrawerScreen<any>|LazyDrawerRoute<any>)[] = [',
      ...Array.from(drawerScreens, (name) => `  ${name},`).sort(),
      '];',
      '',
      'export const StackScreenList: (StackScreen<any> | LazyStackRoute<any>)[] = [',
      ...Array.from(stackScreens, (name) => `  ${name},`).sort(),
      '];',
      '',
    ].join('\n'),
  );
};

const generateBoxes = () => {
  const boxImports = new Map();
  const boxes = new Set();
  cp.execSync("grep -o -E '^export const (\\w+)Box = create(\\w+)?Box[\\(<]' -r ./src", { encoding: 'utf-8' })
    .trim()
    .split('\n')
    .forEach((line) => {
      const [file, , , name] = line.split(/[: ]/g);
      const id = file.replace('./src', '../..').replace(/\.tsx?$/, '');
      if (boxImports.has(id)) {
        boxImports.get(id).add(name);
      } else {
        boxImports.set(id, new Set([name]));
      }
      boxes.add(name);
    });
  fs.outputFileSync(
    './src/entry/generated/boxes.ts',
    [
      `/* This file is generated by ${path.relative(process.cwd(), __filename)}, please do not edit it. */`,
      '',
      "import { Box } from '../../utils/store/box';",
      ...Array.from(boxImports.entries())
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([id, names]) => `import { ${Array.from(names).sort().join(', ')} } from '${id}';`),
      '',
      'export const rootBoxes: Box[] = [',
      ...Array.from(boxes, (name) => `  ${name},`).sort(),
      '].sort((a, b) => a.name.localeCompare(b.name));',
      '',
    ].join('\n'),
  );
};

const generatePaths = async () => {
  const modules = fs.readdirSync('./src/modules');
  for (const mod of modules) {
    const pages = fs.readdirSync(`./src/modules/${mod}`);
    /** @type {string} */
    let api = fs.readFileSync(`./src/modules/${mod}/${mod}.api.ts`, { encoding: 'utf-8' });
    const paths = new Set(['PATH_HOME']);
    for (const page of pages) {
      if (/(\.ts$|store|components|hooks|utils)/.test(page)) {
        continue;
      }
      if (!fs.existsSync(`src/modules/${mod}/${page}/${page}.tsx`)) {
        continue;
      }
      const key = `path${page}`.replace(/([A-Z])/g, '_$1').toUpperCase();
      paths.add(key);
      let route = page.replace(/([A-Z])/g, '/$1').toLowerCase();
      const modRoute =
        '/' +
        mod
          .replace(/([A-Z])/g, '_$1')
          .toLowerCase()
          .substr(1);
      if (!route.startsWith(modRoute)) {
        route = modRoute + route;
      }
      if (api.indexOf(`const ${key} =`) === -1) {
        api += [
          '',
          `export interface ${page}Params {`,
          '}',
          '',
          `export const ${key} = new RoutePath<${page}Params>('${route}');`,
          '',
        ].join('\n');
      }
    }
    const matches = api.match(/PATH_[A-Z_]+/g) || [];
    const bad = matches.filter((p) => !paths.has(p));
    if (bad.length > 0) {
      console.warn('unknown paths in src/modules/%s/%s.api.ts: %s', mod, mod, bad.join(', '));
    }
    fs.writeFileSync(`./src/modules/${mod}/${mod}.api.ts`, api);
  }
};

const transform = async (argv) => {
  const files = [].concat(
    ...argv.files.map((f) => {
      if (f.includes('*')) {
        return G.sync(f);
      }
      const stat = fs.statSync(f);
      if (stat.isDirectory()) {
        return G.sync(path.join(f, '**/*.*'));
      }
      return f;
    }),
  );
  await transformScreen(files);
  await transformClassComponent(files);
  await moveStyleSheet(files);
  await generatePaths();
  await generateScreens();
};

const generate = async () => {
  await generateBoxes();
  await generateScreens();
};

yargs
  .command('transform <files...>', '迁移组件 (screen, class component, stylesheet)', {}, transform)
  .command('styles', '生成 stylePreset.ts', generateStyle)
  .command('generate', '生成入口文件 (boxes, screens)', generate)
  .command('generate-paths', '生成 api 文件', generatePaths)
  .strict()
  .help().argv;
