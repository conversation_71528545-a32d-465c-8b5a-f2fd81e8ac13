#!/usr/bin/env bash
# @since 2021-10-14 16:15:45
# <AUTHOR> <EMAIL>

set -euo pipefail

if [[ "${CONFIGURATION:-X}" == "Debug" ]]; then
  echo "Ignore build checking for debugging..."
  exit 0
fi

ROOT="$(dirname $0)/.."

if [[ "$(/usr/libexec/PlistBuddy -c 'Print :EXUpdatesReleaseChannel' "$ROOT/ios/MoeGo2/Supporting/Expo.plist")" =~ "$(git branch --show-current)" ]]; then
    echo "check EXUpdatesReleaseChannel ok"
else
    echo "ERROR: check EXUpdatesReleaseChannel failed, please run build_prepare.sh manually before archive"
    exit 1
fi

RELEASE_CHANNEL_VERSION="$(/usr/libexec/PlistBuddy -c 'Print :EXUpdatesReleaseChannel' "$ROOT/ios/MoeGo2/Supporting/Expo.plist")"

if [[ $RELEASE_CHANNEL_VERSION == production-v* ]]; then
    square_id="sq0idp-ojCjvDy12EX07-dcKOVf9g"
else
    square_id="sq0idp-nZSpet2hDgW2nXvem_vXyg"
fi
if ! grep $square_id "$ROOT/ios/SquareReaderSDK.xcframework/ios-arm64/SquareReaderSDK.framework/Info.plist"; then
    echo "ERROR: check Square SDK failed, you should run \`git cherry-pick -n commit-square-sdk-staging\` manually before archive."
    exit 1
fi

if ! git show "HEAD:ios/SquareReaderSDK.xcframework/ios-arm64/SquareReaderSDK.framework/Info.plist" | grep sq0idp-ojCjvDy12EX07-dcKOVf9g; then
    echo "ERROR: You should not commit square staging sdk to code."
    exit 1
fi
