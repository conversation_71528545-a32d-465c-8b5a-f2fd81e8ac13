// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/authn/v1/mfa_management_service.proto

package authnpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MfaManagementService_ListAuthenticationFactors_FullMethodName       = "/backend.proto.authn.v1.MfaManagementService/ListAuthenticationFactors"
	MfaManagementService_SendPhoneNumberVerificationCode_FullMethodName = "/backend.proto.authn.v1.MfaManagementService/SendPhoneNumberVerificationCode"
	MfaManagementService_AddPhoneNumberFactor_FullMethodName            = "/backend.proto.authn.v1.MfaManagementService/AddPhoneNumberFactor"
	MfaManagementService_ChangePhoneNumberFactor_FullMethodName         = "/backend.proto.authn.v1.MfaManagementService/ChangePhoneNumberFactor"
	MfaManagementService_DeleteAuthenticationFactor_FullMethodName      = "/backend.proto.authn.v1.MfaManagementService/DeleteAuthenticationFactor"
)

// MfaManagementServiceClient is the client API for MfaManagementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MfaManagementService 提供管理用户多因素认证（MFA）设置的方法。
type MfaManagementServiceClient interface {
	// 列出指定用户注册的所有 MFA 认证方式。
	ListAuthenticationFactors(ctx context.Context, in *ListAuthenticationFactorsRequest, opts ...grpc.CallOption) (*ListAuthenticationFactorsResponse, error)
	// 添加手机号作为 MFA 认证方式的第一步。
	// 此接口会向指定的手机号发送一个验证码，并返回一个 `verification_token` 用于第二步验证。
	SendPhoneNumberVerificationCode(ctx context.Context, in *SendPhoneNumberVerificationCodeRequest, opts ...grpc.CallOption) (*SendPhoneNumberVerificationCodeResponse, error)
	// 添加手机号作为 MFA 认证方式的第二步。
	// 它会验证第一步中发送的验证码，并在成功后创建认证方式。
	AddPhoneNumberFactor(ctx context.Context, in *AddPhoneNumberFactorRequest, opts ...grpc.CallOption) (*AddPhoneNumberFactorResponse, error)
	// 更换已存在的手机号认证方式。
	// 此接口会验证新手机号的验证码，并在成功后原子性地替换旧的手机号因子。
	ChangePhoneNumberFactor(ctx context.Context, in *ChangePhoneNumberFactorRequest, opts ...grpc.CallOption) (*ChangePhoneNumberFactorResponse, error)
	// 删除一个已存在的 MFA 认证方式。
	DeleteAuthenticationFactor(ctx context.Context, in *DeleteAuthenticationFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type mfaManagementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMfaManagementServiceClient(cc grpc.ClientConnInterface) MfaManagementServiceClient {
	return &mfaManagementServiceClient{cc}
}

func (c *mfaManagementServiceClient) ListAuthenticationFactors(ctx context.Context, in *ListAuthenticationFactorsRequest, opts ...grpc.CallOption) (*ListAuthenticationFactorsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAuthenticationFactorsResponse)
	err := c.cc.Invoke(ctx, MfaManagementService_ListAuthenticationFactors_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mfaManagementServiceClient) SendPhoneNumberVerificationCode(ctx context.Context, in *SendPhoneNumberVerificationCodeRequest, opts ...grpc.CallOption) (*SendPhoneNumberVerificationCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPhoneNumberVerificationCodeResponse)
	err := c.cc.Invoke(ctx, MfaManagementService_SendPhoneNumberVerificationCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mfaManagementServiceClient) AddPhoneNumberFactor(ctx context.Context, in *AddPhoneNumberFactorRequest, opts ...grpc.CallOption) (*AddPhoneNumberFactorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddPhoneNumberFactorResponse)
	err := c.cc.Invoke(ctx, MfaManagementService_AddPhoneNumberFactor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mfaManagementServiceClient) ChangePhoneNumberFactor(ctx context.Context, in *ChangePhoneNumberFactorRequest, opts ...grpc.CallOption) (*ChangePhoneNumberFactorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangePhoneNumberFactorResponse)
	err := c.cc.Invoke(ctx, MfaManagementService_ChangePhoneNumberFactor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mfaManagementServiceClient) DeleteAuthenticationFactor(ctx context.Context, in *DeleteAuthenticationFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MfaManagementService_DeleteAuthenticationFactor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MfaManagementServiceServer is the server API for MfaManagementService service.
// All implementations must embed UnimplementedMfaManagementServiceServer
// for forward compatibility.
//
// MfaManagementService 提供管理用户多因素认证（MFA）设置的方法。
type MfaManagementServiceServer interface {
	// 列出指定用户注册的所有 MFA 认证方式。
	ListAuthenticationFactors(context.Context, *ListAuthenticationFactorsRequest) (*ListAuthenticationFactorsResponse, error)
	// 添加手机号作为 MFA 认证方式的第一步。
	// 此接口会向指定的手机号发送一个验证码，并返回一个 `verification_token` 用于第二步验证。
	SendPhoneNumberVerificationCode(context.Context, *SendPhoneNumberVerificationCodeRequest) (*SendPhoneNumberVerificationCodeResponse, error)
	// 添加手机号作为 MFA 认证方式的第二步。
	// 它会验证第一步中发送的验证码，并在成功后创建认证方式。
	AddPhoneNumberFactor(context.Context, *AddPhoneNumberFactorRequest) (*AddPhoneNumberFactorResponse, error)
	// 更换已存在的手机号认证方式。
	// 此接口会验证新手机号的验证码，并在成功后原子性地替换旧的手机号因子。
	ChangePhoneNumberFactor(context.Context, *ChangePhoneNumberFactorRequest) (*ChangePhoneNumberFactorResponse, error)
	// 删除一个已存在的 MFA 认证方式。
	DeleteAuthenticationFactor(context.Context, *DeleteAuthenticationFactorRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedMfaManagementServiceServer()
}

// UnimplementedMfaManagementServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMfaManagementServiceServer struct{}

func (UnimplementedMfaManagementServiceServer) ListAuthenticationFactors(context.Context, *ListAuthenticationFactorsRequest) (*ListAuthenticationFactorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuthenticationFactors not implemented")
}
func (UnimplementedMfaManagementServiceServer) SendPhoneNumberVerificationCode(context.Context, *SendPhoneNumberVerificationCodeRequest) (*SendPhoneNumberVerificationCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPhoneNumberVerificationCode not implemented")
}
func (UnimplementedMfaManagementServiceServer) AddPhoneNumberFactor(context.Context, *AddPhoneNumberFactorRequest) (*AddPhoneNumberFactorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPhoneNumberFactor not implemented")
}
func (UnimplementedMfaManagementServiceServer) ChangePhoneNumberFactor(context.Context, *ChangePhoneNumberFactorRequest) (*ChangePhoneNumberFactorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePhoneNumberFactor not implemented")
}
func (UnimplementedMfaManagementServiceServer) DeleteAuthenticationFactor(context.Context, *DeleteAuthenticationFactorRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAuthenticationFactor not implemented")
}
func (UnimplementedMfaManagementServiceServer) mustEmbedUnimplementedMfaManagementServiceServer() {}
func (UnimplementedMfaManagementServiceServer) testEmbeddedByValue()                              {}

// UnsafeMfaManagementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MfaManagementServiceServer will
// result in compilation errors.
type UnsafeMfaManagementServiceServer interface {
	mustEmbedUnimplementedMfaManagementServiceServer()
}

func RegisterMfaManagementServiceServer(s grpc.ServiceRegistrar, srv MfaManagementServiceServer) {
	// If the following call pancis, it indicates UnimplementedMfaManagementServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MfaManagementService_ServiceDesc, srv)
}

func _MfaManagementService_ListAuthenticationFactors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuthenticationFactorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MfaManagementServiceServer).ListAuthenticationFactors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MfaManagementService_ListAuthenticationFactors_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MfaManagementServiceServer).ListAuthenticationFactors(ctx, req.(*ListAuthenticationFactorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MfaManagementService_SendPhoneNumberVerificationCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPhoneNumberVerificationCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MfaManagementServiceServer).SendPhoneNumberVerificationCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MfaManagementService_SendPhoneNumberVerificationCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MfaManagementServiceServer).SendPhoneNumberVerificationCode(ctx, req.(*SendPhoneNumberVerificationCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MfaManagementService_AddPhoneNumberFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPhoneNumberFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MfaManagementServiceServer).AddPhoneNumberFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MfaManagementService_AddPhoneNumberFactor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MfaManagementServiceServer).AddPhoneNumberFactor(ctx, req.(*AddPhoneNumberFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MfaManagementService_ChangePhoneNumberFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePhoneNumberFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MfaManagementServiceServer).ChangePhoneNumberFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MfaManagementService_ChangePhoneNumberFactor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MfaManagementServiceServer).ChangePhoneNumberFactor(ctx, req.(*ChangePhoneNumberFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MfaManagementService_DeleteAuthenticationFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAuthenticationFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MfaManagementServiceServer).DeleteAuthenticationFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MfaManagementService_DeleteAuthenticationFactor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MfaManagementServiceServer).DeleteAuthenticationFactor(ctx, req.(*DeleteAuthenticationFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MfaManagementService_ServiceDesc is the grpc.ServiceDesc for MfaManagementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MfaManagementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.authn.v1.MfaManagementService",
	HandlerType: (*MfaManagementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAuthenticationFactors",
			Handler:    _MfaManagementService_ListAuthenticationFactors_Handler,
		},
		{
			MethodName: "SendPhoneNumberVerificationCode",
			Handler:    _MfaManagementService_SendPhoneNumberVerificationCode_Handler,
		},
		{
			MethodName: "AddPhoneNumberFactor",
			Handler:    _MfaManagementService_AddPhoneNumberFactor_Handler,
		},
		{
			MethodName: "ChangePhoneNumberFactor",
			Handler:    _MfaManagementService_ChangePhoneNumberFactor_Handler,
		},
		{
			MethodName: "DeleteAuthenticationFactor",
			Handler:    _MfaManagementService_DeleteAuthenticationFactor_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/authn/v1/mfa_management_service.proto",
}
