package db

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

// AuthenticationFactorReadWriter defines the interface for reading and writing authentication factors.
type AuthenticationFactorReadWriter interface {
	ListAuthenticationFactors(ctx context.Context, accountID int64) ([]*AuthenticationFactor, error)
	GetPhoneNumberFactor(ctx context.Context, accountID int64) (*AuthenticationFactor, error)
	CreateAuthenticationFactor(ctx context.Context, factor *AuthenticationFactor) error
	DeleteAuthenticationFactor(ctx context.Context, id string) error
}

func (d *database) ListAuthenticationFactors(ctx context.Context, accountID int64) ([]*AuthenticationFactor, error) {
	var factors []*AuthenticationFactor
	if err := d.db.WithContext(ctx).
		// GORM will automatically add the "deleted_at IS NULL" condition for soft delete.
		Where("account_id = ?", accountID).
		Find(&factors).
		Error; err != nil {
		return nil, err
	}

	return factors, nil
}

func (d *database) GetPhoneNumberFactor(ctx context.Context,
	accountID int64) (*AuthenticationFactor, error) {
	var factor AuthenticationFactor
	err := d.db.WithContext(ctx).
		// GORM will automatically add the "deleted_at IS NULL" condition for soft delete.
		Where("account_id = ? AND type = ?", accountID, FactorTypePhoneNumber).
		First(&factor).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	return &factor, nil
}

func (d *database) CreateAuthenticationFactor(ctx context.Context, factor *AuthenticationFactor) error {
	return d.db.WithContext(ctx).Create(factor).Error
}

func (d *database) DeleteAuthenticationFactor(ctx context.Context, id string) error {
	// gorm 会自动根据 gorm.DeletedAt 类型使用软删除
	return d.db.WithContext(ctx).Delete(&AuthenticationFactor{}, "id = ?", id).Error
}
