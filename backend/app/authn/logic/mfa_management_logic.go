package logic

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/authn/repo/cache"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/message"
	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

const (
	// nolint:lll
	// phoneNumberBindingContentTmpl is the SMS content for phone number binding verification.
	phoneNumberBindingContentTmpl = "[MoeGo] Your verification code is %s. Use this code to verify your phone number for two-factor authentication. Do not share this code with anyone."
)

type MfaManagementLogic struct {
	db         db.Database
	codeCache  cache.VerificationCodeRepo
	messageAPI message.API
}

// newMfaManagementLogic is the constructor for internal and test use.
// It accepts all dependencies as arguments, making it suitable for mocking.
func newMfaManagementLogic(
	db db.Database,
	codeCache cache.VerificationCodeRepo,
	messageAPI message.API,
) *MfaManagementLogic {
	return &MfaManagementLogic{
		db:         db,
		codeCache:  codeCache,
		messageAPI: messageAPI,
	}
}

// NewMfaManagementLogic is the constructor for production use.
// It initializes dependencies from global sources (e.g., config.Get()).
func NewMfaManagementLogic() *MfaManagementLogic {
	codeCache, err := cache.NewVerificationCodeRepo()
	if err != nil {
		panic(err)
	}

	return newMfaManagementLogic(
		db.New(),
		codeCache,
		message.NewAPI(),
	)
}

func (l *MfaManagementLogic) ListAuthenticationFactors(ctx context.Context, accountID int64) (
	[]*authnpb.AuthenticationFactor, error) {
	factors, err := l.db.ListAuthenticationFactors(ctx, accountID)
	if err != nil {
		return nil, err
	}

	var pbFactors []*authnpb.AuthenticationFactor
	for _, factor := range factors {
		pbFactor := &authnpb.AuthenticationFactor{
			Id: factor.ID,
		}

		switch factor.Type {
		case db.FactorTypePhoneNumber:
			pbFactor.Type = authnpb.FactorType_PHONE_NUMBER
			if details, ok := factor.ParsedDetails.(db.PhoneNumberDetails); ok {
				pbFactor.Details = &authnpb.AuthenticationFactor_PhoneNumber_{
					PhoneNumber: &authnpb.AuthenticationFactor_PhoneNumber{
						E164Number: details.E164Number,
						RegionCode: details.RegionCode,
					},
				}
			}
		default:
			pbFactor.Type = authnpb.FactorType_FACTOR_TYPE_UNSPECIFIED
		}
		pbFactors = append(pbFactors, pbFactor)
	}

	return pbFactors, nil
}

func (l *MfaManagementLogic) AddPhoneNumberFactor(ctx context.Context, req *authnpb.AddPhoneNumberFactorRequest) (
	*authnpb.AuthenticationFactor, error) {
	if !l.codeCache.Verify(ctx,
		cache.PurposePhoneNumberBinding, req.GetVerificationToken(), req.GetVerificationCode(), req.GetE164Number()) {
		return nil, authnutils.VerificationCodeMismatchError
	}

	factor := &db.AuthenticationFactor{
		ID:        authnutils.NewUUIDv7(),
		AccountID: req.GetAccountId(),
		Type:      db.FactorTypePhoneNumber,
		ParsedDetails: db.PhoneNumberDetails{
			E164Number: req.GetE164Number(),
			RegionCode: req.GetRegionCode(),
		},
	}

	err := l.db.WithTx(ctx, func(repo db.Database) error {
		if err := repo.CreateAuthenticationFactor(ctx, factor); err != nil {
			// 可能存在并发或者脏数据导致唯一键冲突，捕获并抛出错误
			if db.IsUniqueConstraintViolationError(err) {
				return authnutils.PhoneNumberFactorAlreadyExistsError
			}
			log.ErrorContextf(ctx, "failed to create phone number factor for account %d: %v", req.AccountId, err)

			return authnutils.CreatePhoneNumberFactorError
		}

		l.upsertTrustedDeviceIfProvided(ctx, repo, req.GetAccountId(), req.GetDeviceId())

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &authnpb.AuthenticationFactor{
		Id:   factor.ID,
		Type: authnpb.FactorType_PHONE_NUMBER,
		Details: &authnpb.AuthenticationFactor_PhoneNumber_{
			PhoneNumber: &authnpb.AuthenticationFactor_PhoneNumber{
				E164Number: req.E164Number,
				RegionCode: req.RegionCode,
			},
		},
	}, nil
}

func (l *MfaManagementLogic) ChangePhoneNumberFactor(ctx context.Context, req *authnpb.ChangePhoneNumberFactorRequest) (
	*authnpb.AuthenticationFactor, error) {
	if !l.codeCache.Verify(ctx,
		cache.PurposePhoneNumberBinding, req.GetVerificationToken(), req.GetVerificationCode(), req.GetE164Number()) {
		return nil, authnutils.VerificationCodeMismatchError
	}

	newFactor := &db.AuthenticationFactor{
		ID:        authnutils.NewUUIDv7(),
		AccountID: req.GetAccountId(),
		Type:      db.FactorTypePhoneNumber,
		ParsedDetails: db.PhoneNumberDetails{
			E164Number: req.GetE164Number(),
			RegionCode: req.GetRegionCode(),
		},
	}

	err := l.db.WithTx(ctx, func(repo db.Database) error {
		// 1. Find the existing phone number factor.
		oldFactor, err := repo.GetPhoneNumberFactor(ctx, req.GetAccountId())
		if err != nil {
			log.ErrorContextf(ctx, "failed to get phone number factor for account %d: %v", req.AccountId, err)

			return err // Return a generic error
		}
		if oldFactor == nil {
			return authnutils.NoPhoneNumberFactorError
		}

		// 2. Delete the old factor.
		if err := repo.DeleteAuthenticationFactor(ctx, oldFactor.ID); err != nil {
			log.ErrorContextf(ctx, "failed to delete old phone number factor for account %d: %v", req.AccountId, err)

			return err
		}

		// 3. Create the new factor.
		if err := repo.CreateAuthenticationFactor(ctx, newFactor); err != nil {
			log.ErrorContextf(ctx, "failed to create new phone number factor for account %d: %v", req.AccountId, err)
			// No need to check for unique constraint violation here, as we just deleted the old one.
			// Any error here is unexpected.
			return authnutils.CreatePhoneNumberFactorError
		}

		// 4. Upsert the device as a trusted device if device_id is provided.
		l.upsertTrustedDeviceIfProvided(ctx, repo, req.GetAccountId(), req.GetDeviceId())

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &authnpb.AuthenticationFactor{
		Id:   newFactor.ID,
		Type: authnpb.FactorType_PHONE_NUMBER,
		Details: &authnpb.AuthenticationFactor_PhoneNumber_{
			PhoneNumber: &authnpb.AuthenticationFactor_PhoneNumber{
				E164Number: req.E164Number,
				RegionCode: req.RegionCode,
			},
		},
	}, nil
}

// SendPhoneNumberVerificationCode sends a verification code to the user's phone number.
func (l *MfaManagementLogic) SendPhoneNumberVerificationCode(ctx context.Context,
	req *authnpb.SendPhoneNumberVerificationCodeRequest) (string, error) {
	token, code, err := l.codeCache.Create(ctx, cache.PurposePhoneNumberBinding, req.AccountId, req.E164Number)
	if err != nil {
		return "", err
	}

	content := fmt.Sprintf(phoneNumberBindingContentTmpl, code)
	if err := l.messageAPI.SendVerificationCode(ctx, req.RegionCode, req.E164Number, content); err != nil {
		return "", err
	}

	return token, nil
}

func (l *MfaManagementLogic) DeleteAuthenticationFactor(ctx context.Context, id string) error {
	return l.db.DeleteAuthenticationFactor(ctx, id)
}

// upsertTrustedDeviceIfProvided is a helper function to upsert a trusted device if a device ID is provided.
// This operation is non-critical, so any errors are logged but do not fail the parent operation.
func (l *MfaManagementLogic) upsertTrustedDeviceIfProvided(ctx context.Context,
	repo db.Database, accountID int64, deviceID string) {
	if len(deviceID) > 0 {
		device := &db.TrustedDevice{
			ID:        authnutils.NewUUIDv7(),
			AccountID: accountID,
			DeviceID:  deviceID,
		}
		if err := repo.UpsertTrustedDevice(ctx, device); err != nil {
			// Trusting a device is a non-critical operation.
			// If it fails, we should log the error but not fail the entire request.
			log.ErrorContextf(ctx, "failed to upsert trusted device for account %d: %v", accountID, err)
		}
	}
}
