// Package logic_test contains the unit tests for the logic package.
package logic

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/authn/repo/cache"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	mockcache "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/cache"
	mockdb "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/db"
	mockmessage "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/message"
	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

// mfaLogicTestSuite is a test suite for the MfaManagementLogic, using testify/suite.
type mfaLogicTestSuite struct {
	suite.Suite    // Embed the suite.Suite to get testing capabilities.
	ctrl           *gomock.Controller
	mockDB         *mockdb.MockDatabase
	mockCodeCache  *mockcache.MockVerificationCodeRepo
	mockMessageAPI *mockmessage.MockAPI
	logic          *MfaManagementLogic
}

// SetupTest is run before each test in the suite.
func (s *mfaLogicTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockDB = mockdb.NewMockDatabase(s.ctrl)
	s.mockCodeCache = mockcache.NewMockVerificationCodeRepo(s.ctrl)
	s.mockMessageAPI = mockmessage.NewMockAPI(s.ctrl)
	s.logic = newMfaManagementLogic(s.mockDB, s.mockCodeCache, s.mockMessageAPI)
}

// TearDownTest is run after each test in the suite.
func (s *mfaLogicTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// TestMfaLogicTestSuite runs the entire test suite.
func TestMfaLogicTestSuite(t *testing.T) {
	suite.Run(t, new(mfaLogicTestSuite))
}

// mockTx mocks the transaction behavior.
func (s *mfaLogicTestSuite) mockTx(errToReturn error) {
	s.mockDB.EXPECT().WithTx(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, fn func(repo db.Database) error) error {
			// In the mock, we just execute the function directly with the same mock DB instance.
			// This simulates the transaction block.
			if err := fn(s.mockDB); err != nil {
				return err
			}
			return errToReturn
		})
}

// TestAddPhoneNumberFactor_Success_WithDeviceID tests the successful addition of a phone number factor with a device ID.
func (s *mfaLogicTestSuite) TestAddPhoneNumberFactor_Success_WithDeviceID() {
	// --- Arrange ---
	req := &authnpb.AddPhoneNumberFactorRequest{
		AccountId:         123,
		VerificationToken: "test-token",
		VerificationCode:  "123456",
		E164Number:        "+***********",
		RegionCode:        "US",
		DeviceId:          pointer.Get("test-device-id"),
	}
	s.mockCodeCache.EXPECT().Verify(gomock.Any(),
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number).Return(true)

	// Mock the transaction and the calls within it
	s.mockTx(nil)
	s.mockDB.EXPECT().CreateAuthenticationFactor(gomock.Any(), gomock.Any()).Return(nil)
	s.mockDB.EXPECT().UpsertTrustedDevice(gomock.Any(), gomock.Any()).Return(nil)

	// --- Act ---
	result, err := s.logic.AddPhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Equal(authnpb.FactorType_PHONE_NUMBER, result.Type)
}

// TestAddPhoneNumberFactor_Success_WithoutDeviceID tests the successful addition of a phone number factor without a device ID.
func (s *mfaLogicTestSuite) TestAddPhoneNumberFactor_Success_WithoutDeviceID() {
	// --- Arrange ---
	req := &authnpb.AddPhoneNumberFactorRequest{
		AccountId:         123,
		VerificationToken: "test-token",
		VerificationCode:  "123456",
		E164Number:        "+***********",
		RegionCode:        "US",
		DeviceId:          nil,
	}
	s.mockCodeCache.EXPECT().Verify(gomock.Any(),
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number).Return(true)

	// Mock the transaction and the calls within it
	s.mockTx(nil)
	s.mockDB.EXPECT().CreateAuthenticationFactor(gomock.Any(), gomock.Any()).Return(nil)
	// No call to UpsertTrustedDevice is expected.

	// --- Act ---
	result, err := s.logic.AddPhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Equal(authnpb.FactorType_PHONE_NUMBER, result.Type)
}

// TestAddPhoneNumberFactor_Failure_VerificationFailed tests the case where code verification fails.
func (s *mfaLogicTestSuite) TestAddPhoneNumberFactor_Failure_VerificationFailed() {
	// --- Arrange ---
	req := &authnpb.AddPhoneNumberFactorRequest{
		VerificationToken: "invalid-token",
		VerificationCode:  "wrong-code",
		E164Number:        "+***********",
	}
	expectedErr := authnutils.VerificationCodeMismatchError
	s.mockCodeCache.EXPECT().Verify(gomock.Any(),
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number).Return(false)

	// --- Act ---
	result, err := s.logic.AddPhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(expectedErr, err)
}

// TestAddPhoneNumberFactor_Failure_DBError tests database failure on create.
func (s *mfaLogicTestSuite) TestAddPhoneNumberFactor_Failure_DBError() {
	// --- Arrange ---
	req := &authnpb.AddPhoneNumberFactorRequest{
		VerificationToken: "test-token",
		VerificationCode:  "123456",
		E164Number:        "+***********",
	}
	s.mockCodeCache.EXPECT().Verify(gomock.Any(),
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number).Return(true)

	// Mock the transaction to return an error
	dbErr := errors.New("database connection failed")
	s.mockTx(dbErr) // This simulates the transaction failing
	s.mockDB.EXPECT().CreateAuthenticationFactor(gomock.Any(), gomock.Any()).Return(dbErr)

	// --- Act ---
	result, err := s.logic.AddPhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(authnutils.CreatePhoneNumberFactorError, err)
}

// TestAddPhoneNumberFactor_Failure_AlreadyExists tests the case where the phone number factor already exists.
func (s *mfaLogicTestSuite) TestAddPhoneNumberFactor_Failure_AlreadyExists() {
	// --- Arrange ---
	req := &authnpb.AddPhoneNumberFactorRequest{
		VerificationToken: "test-token",
		VerificationCode:  "123456",
		E164Number:        "+***********",
	}
	s.mockCodeCache.EXPECT().Verify(gomock.Any(),
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number).Return(true)

	// Mock the transaction and the unique constraint violation error within it
	uniqueConstraintErr := &pgconn.PgError{Code: "23505"}
	s.mockTx(uniqueConstraintErr) // This simulates the transaction failing with a specific error
	s.mockDB.EXPECT().CreateAuthenticationFactor(gomock.Any(), gomock.Any()).Return(uniqueConstraintErr)

	// --- Act ---
	result, err := s.logic.AddPhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(authnutils.PhoneNumberFactorAlreadyExistsError, err)
}

// TestChangePhoneNumberFactor_Success tests the successful change of a phone number factor.
func (s *mfaLogicTestSuite) TestChangePhoneNumberFactor_Success() {
	// --- Arrange ---
	req := &authnpb.ChangePhoneNumberFactorRequest{
		AccountId:         123,
		VerificationToken: "test-token",
		VerificationCode:  "123456",
		E164Number:        "+***********", // New number
		RegionCode:        "US",
	}
	oldFactor := &db.AuthenticationFactor{ID: "old-factor-id"}

	s.mockCodeCache.EXPECT().Verify(gomock.Any(),
		cache.PurposePhoneNumberBinding, req.VerificationToken, req.VerificationCode, req.E164Number).Return(true)

	s.mockTx(nil)
	s.mockDB.EXPECT().GetPhoneNumberFactor(gomock.Any(), req.AccountId).Return(oldFactor, nil)
	s.mockDB.EXPECT().DeleteAuthenticationFactor(gomock.Any(), oldFactor.ID).Return(nil)
	s.mockDB.EXPECT().CreateAuthenticationFactor(gomock.Any(), gomock.Any()).Return(nil)

	// --- Act ---
	result, err := s.logic.ChangePhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Equal(authnpb.FactorType_PHONE_NUMBER, result.Type)
	s.Equal(req.E164Number, result.GetPhoneNumber().GetE164Number())
}

// TestChangePhoneNumberFactor_Failure_VerificationFailed tests the case where code verification fails.
func (s *mfaLogicTestSuite) TestChangePhoneNumberFactor_Failure_VerificationFailed() {
	// --- Arrange ---
	req := &authnpb.ChangePhoneNumberFactorRequest{
		VerificationToken: "invalid-token",
		VerificationCode:  "wrong-code",
	}
	s.mockCodeCache.EXPECT().Verify(gomock.Any(), gomock.Any(), req.VerificationToken, req.VerificationCode, gomock.Any()).Return(false)

	// --- Act ---
	result, err := s.logic.ChangePhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(authnutils.VerificationCodeMismatchError, err)
}

// TestChangePhoneNumberFactor_Failure_NoExistingFactor tests the case where no existing phone factor is found.
func (s *mfaLogicTestSuite) TestChangePhoneNumberFactor_Failure_NoExistingFactor() {
	// --- Arrange ---
	req := &authnpb.ChangePhoneNumberFactorRequest{AccountId: 123, VerificationToken: "token", VerificationCode: "123456"}
	s.mockCodeCache.EXPECT().Verify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true)

	s.mockTx(authnutils.NoPhoneNumberFactorError)
	s.mockDB.EXPECT().GetPhoneNumberFactor(gomock.Any(), req.AccountId).Return(nil, nil)

	// --- Act ---
	result, err := s.logic.ChangePhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(authnutils.NoPhoneNumberFactorError, err)
}

// TestChangePhoneNumberFactor_Failure_DBError tests database failure during the transaction.
func (s *mfaLogicTestSuite) TestChangePhoneNumberFactor_Failure_DBError() {
	// --- Arrange ---
	req := &authnpb.ChangePhoneNumberFactorRequest{AccountId: 123, VerificationToken: "token", VerificationCode: "123456"}
	oldFactor := &db.AuthenticationFactor{ID: "old-factor-id"}
	dbErr := errors.New("db connection failed")

	s.mockCodeCache.EXPECT().Verify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true)

	s.mockTx(dbErr)
	s.mockDB.EXPECT().GetPhoneNumberFactor(gomock.Any(), req.AccountId).Return(oldFactor, nil)
	s.mockDB.EXPECT().DeleteAuthenticationFactor(gomock.Any(), oldFactor.ID).Return(dbErr)

	// --- Act ---
	result, err := s.logic.ChangePhoneNumberFactor(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(dbErr, err)
}

// TestSendPhoneNumberVerificationCode_Success tests successful code sending.
func (s *mfaLogicTestSuite) TestSendPhoneNumberVerificationCode_Success() {
	// --- Arrange ---
	req := &authnpb.SendPhoneNumberVerificationCodeRequest{
		AccountId:  123,
		E164Number: "+***********",
		RegionCode: "US",
	}
	expectedToken := "new-token"
	expectedCode := "654321"
	expectedContent := fmt.Sprintf(phoneNumberBindingContentTmpl, expectedCode)

	s.mockCodeCache.EXPECT().Create(gomock.Any(), cache.PurposePhoneNumberBinding, req.AccountId, req.E164Number).Return(expectedToken, expectedCode, nil)
	s.mockMessageAPI.EXPECT().SendVerificationCode(gomock.Any(), req.RegionCode, req.E164Number, expectedContent).Return(nil)

	// --- Act ---
	token, err := s.logic.SendPhoneNumberVerificationCode(context.Background(), req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Equal(expectedToken, token)
}

// TestSendPhoneNumberVerificationCode_Failure_CacheError tests cache failure on store.
func (s *mfaLogicTestSuite) TestSendPhoneNumberVerificationCode_Failure_CacheError() {
	// --- Arrange ---
	req := &authnpb.SendPhoneNumberVerificationCodeRequest{AccountId: 123, E164Number: "+***********"}
	expectedErr := errors.New("cache write failed")
	s.mockCodeCache.EXPECT().Create(gomock.Any(), cache.PurposePhoneNumberBinding, req.AccountId, req.E164Number).Return("", "", expectedErr)

	// --- Act ---
	token, err := s.logic.SendPhoneNumberVerificationCode(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Empty(token)
	s.Equal(expectedErr, err)
}

// TestSendPhoneNumberVerificationCode_Failure_MessageError tests message sending failure.
func (s *mfaLogicTestSuite) TestSendPhoneNumberVerificationCode_Failure_MessageError() {
	// --- Arrange ---
	req := &authnpb.SendPhoneNumberVerificationCodeRequest{
		AccountId:  123,
		E164Number: "+***********",
		RegionCode: "US",
	}
	expectedToken := "new-token"
	expectedCode := "654321"
	expectedContent := fmt.Sprintf(phoneNumberBindingContentTmpl, expectedCode)
	expectedErr := errors.New("sms service unavailable")
	s.mockCodeCache.EXPECT().Create(gomock.Any(), cache.PurposePhoneNumberBinding, req.AccountId, req.E164Number).Return(expectedToken, expectedCode, nil)
	s.mockMessageAPI.EXPECT().SendVerificationCode(gomock.Any(), req.RegionCode, req.E164Number, expectedContent).Return(expectedErr)

	// --- Act ---
	token, err := s.logic.SendPhoneNumberVerificationCode(context.Background(), req)

	// --- Assert ---
	s.Require().Error(err)
	s.Empty(token)
	s.Equal(expectedErr, err)
}

// TestListAuthenticationFactors_Success tests successful factor listing.
func (s *mfaLogicTestSuite) TestListAuthenticationFactors_Success() {
	// --- Arrange ---
	accountID := int64(456)
	dbFactors := []*db.AuthenticationFactor{
		{
			ID: "factor-1", AccountID: accountID, Type: db.FactorTypePhoneNumber,
			ParsedDetails: db.PhoneNumberDetails{E164Number: "+***********", RegionCode: "US"},
		},
		{ID: "factor-2", AccountID: accountID, Type: "some_other_type"},
	}
	s.mockDB.EXPECT().ListAuthenticationFactors(gomock.Any(), accountID).Return(dbFactors, nil)

	// --- Act ---
	result, err := s.logic.ListAuthenticationFactors(context.Background(), accountID)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().Len(result, 2)
	s.Equal("factor-1", result[0].Id)
	s.Equal(authnpb.FactorType_PHONE_NUMBER, result[0].Type)
	phoneDetails, ok := result[0].Details.(*authnpb.AuthenticationFactor_PhoneNumber_)
	s.Require().True(ok)
	s.Equal("+***********", phoneDetails.PhoneNumber.E164Number)
	s.Equal("US", phoneDetails.PhoneNumber.RegionCode)
	s.Equal("factor-2", result[1].Id)
	s.Equal(authnpb.FactorType_FACTOR_TYPE_UNSPECIFIED, result[1].Type)
	s.Nil(result[1].Details)
}

// TestListAuthenticationFactors_Failure_DBError tests database failure on list.
func (s *mfaLogicTestSuite) TestListAuthenticationFactors_Failure_DBError() {
	// --- Arrange ---
	accountID := int64(789)
	expectedErr := errors.New("database list failed")
	s.mockDB.EXPECT().ListAuthenticationFactors(gomock.Any(), accountID).Return(nil, expectedErr)

	// --- Act ---
	result, err := s.logic.ListAuthenticationFactors(context.Background(), accountID)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(result)
	s.Equal(expectedErr, err)
}

// TestDeleteAuthenticationFactor_Success tests successful factor deletion.
func (s *mfaLogicTestSuite) TestDeleteAuthenticationFactor_Success() {
	// --- Arrange ---
	factorID := "factor-to-delete"
	s.mockDB.EXPECT().DeleteAuthenticationFactor(gomock.Any(), factorID).Return(nil)

	// --- Act ---
	err := s.logic.DeleteAuthenticationFactor(context.Background(), factorID)

	// --- Assert ---
	s.Require().NoError(err)
}

// TestDeleteAuthenticationFactor_Failure_DBError tests database failure on delete.
func (s *mfaLogicTestSuite) TestDeleteAuthenticationFactor_Failure_DBError() {
	// --- Arrange ---
	factorID := "factor-to-delete"
	expectedErr := errors.New("database delete failed")
	s.mockDB.EXPECT().DeleteAuthenticationFactor(gomock.Any(), factorID).Return(expectedErr)

	// --- Act ---
	err := s.logic.DeleteAuthenticationFactor(context.Background(), factorID)

	// --- Assert ---
	s.Require().Error(err)
	s.Equal(expectedErr, err)
}
