diff --git a/android/src/main/java/expo/modules/ReactActivityDelegateWrapper.kt b/android/src/main/java/expo/modules/ReactActivityDelegateWrapper.kt
index 3dd30cca8473710d2629d53c4ceda164bf09c5d1..267b594b24ed7dfddf05492a74830cd956bbe3d3 100644
--- a/android/src/main/java/expo/modules/ReactActivityDelegateWrapper.kt
+++ b/android/src/main/java/expo/modules/ReactActivityDelegateWrapper.kt
@@ -8,6 +8,8 @@ import android.os.Bundle
 import android.view.KeyEvent
 import android.view.ViewGroup
 import androidx.collection.ArrayMap
+import com.facebook.react.common.LifecycleState
+import java.lang.reflect.InvocationTargetException
 import com.facebook.react.ReactActivity
 import com.facebook.react.ReactActivityDelegate
 import com.facebook.react.ReactDelegate
@@ -200,7 +202,18 @@ class ReactActivityDelegateWrapper(
     reactActivityLifecycleListeners.forEach { listener ->
       listener.onPause(activity)
     }
-    return invokeDelegateMethod("onPause")
+    
+    // fix： https://github.com/expo/expo/pull/15529
+    try {
+      return invokeDelegateMethod("onPause")
+    } catch (e: InvocationTargetException) {
+      if (e.cause is AssertionError && delegate.reactInstanceManager.lifecycleState != LifecycleState.RESUMED) {
+        // NOTE(kudo): workaround exception from `ReactInstanceManager.onHostPause()`,
+        // but i still don't know how to reproduce and how it happens.
+        return
+      }
+      throw e
+    }
   }
 
   override fun onDestroy() {
