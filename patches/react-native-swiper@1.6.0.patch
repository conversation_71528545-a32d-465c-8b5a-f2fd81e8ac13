diff --git a/index.d.ts b/index.d.ts
index 24905e3019a446492d92703e2feab8a79849eb93..4a820a7e4c5e4daaa7e0d0f5f94ebd39e981fc81 100644
--- a/index.d.ts
+++ b/index.d.ts
@@ -3,7 +3,8 @@ import {
   StyleProp,
   NativeSyntheticEvent,
   NativeScrollEvent,
-  ScrollViewProps
+  ScrollViewProps,
+  ScrollView
 } from 'react-native'
 import { Component } from 'react'
 
@@ -138,6 +139,8 @@ declare module 'react-native-swiper' {
     automaticallyAdjustContentInsets?: boolean
     // Enables/Disables swiping
     scrollEnabled?: boolean
+    // customScrollView
+    customScrollView?: any
   }
 
   export default class Swiper extends Component<SwiperProps> {
diff --git a/src/index.js b/src/index.js
index 3e63ca7385bfe1bd6028a4e9e4636e8f1a7b07a8..58761392cb089fdee7a8c170cf360acc94045379 100644
--- a/src/index.js
+++ b/src/index.js
@@ -7,8 +7,7 @@ import PropTypes from 'prop-types'
 import {
   Text,
   View,
-  ViewPropTypes,
-  ScrollView,
+  ScrollView as NativeScrollView,
   Dimensions,
   TouchableOpacity,
   Platform,
@@ -146,7 +145,8 @@ export default class extends Component {
     /**
      * Called when the index has changed because the user swiped.
      */
-    onIndexChanged: PropTypes.func
+    onIndexChanged: PropTypes.func,
+    customScrollView: PropTypes.any,
   }
 
   /**
@@ -174,6 +174,7 @@ export default class extends Component {
     autoplayTimeout: 2.5,
     autoplayDirection: true,
     index: 0,
+    customScrollView: NativeScrollView,
     onIndexChanged: () => null
   }
 
@@ -766,6 +767,7 @@ export default class extends Component {
   }
 
   renderScrollView = pages => {
+    const { customScrollView: ScrollView } = this.props
     return (
       <ScrollView
         ref={this.refScrollView}
