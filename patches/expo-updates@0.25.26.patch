diff --git a/android/src/main/java/expo/modules/updates/loader/Loader.kt b/android/src/main/java/expo/modules/updates/loader/Loader.kt
index ed41b7d66a..d4d1db1915 100644
--- a/android/src/main/java/expo/modules/updates/loader/Loader.kt
+++ b/android/src/main/java/expo/modules/updates/loader/Loader.kt
@@ -200,6 +200,8 @@ abstract class Loader protected constructor(
 
     if (existingUpdateEntity != null && existingUpdateEntity.status == UpdateStatus.READY) {
       // hooray, we already have this update downloaded and ready to go!
+      existingUpdateEntity.commitTime = newUpdateEntity.commitTime
+      database.updateDao().setUpdateCommitTime(existingUpdateEntity, newUpdateEntity.commitTime)
       updateEntity = existingUpdateEntity
       finishWithSuccess()
     } else {
