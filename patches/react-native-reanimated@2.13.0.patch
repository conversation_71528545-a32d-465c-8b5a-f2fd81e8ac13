diff --git a/src/createAnimatedComponent/InlinePropManager.ts b/src/createAnimatedComponent/InlinePropManager.ts
index 70df5cc53..62dd90d22 100644
--- a/src/createAnimatedComponent/InlinePropManager.ts
+++ b/src/createAnimatedComponent/InlinePropManager.ts
@@ -107,9 +107,9 @@ export function hasInlineStyles(style: StyleProps): boolean {
 
 export function getInlineStyle(
   style: Record<string, unknown>,
-  shouldGetInitialStyle: boolean
+  isFirstRender: boolean
 ) {
-  if (shouldGetInitialStyle) {
+  if (isFirstRender) {
     return getInlinePropsUpdate(style);
   }
   const newStyle: StyleProps = {};
diff --git a/src/createAnimatedComponent/PropsFilter.tsx b/src/createAnimatedComponent/PropsFilter.tsx
index e1d949398..2e98ba9fd 100644
--- a/src/createAnimatedComponent/PropsFilter.tsx
+++ b/src/createAnimatedComponent/PropsFilter.tsx
@@ -1,6 +1,5 @@
 'use strict';
 
-import { shallowEqual } from '../hook/utils';
 import type { StyleProps } from '../commonTypes';
 import { isSharedValue } from '../isSharedValue';
 import { isChromeDebugger } from '../PlatformChecker';
@@ -24,30 +23,23 @@ function dummyListener() {
 
 export class PropsFilter implements IPropsFilter {
   private _initialStyle = {};
-  private _previousProps: React.Component['props'] | null = null;
-  private _requiresNewInitials = true;
 
   public filterNonAnimatedProps(
     component: React.Component<unknown, unknown> & IAnimatedComponentInternal
   ): Record<string, unknown> {
     const inputProps =
       component.props as AnimatedComponentProps<InitialComponentProps>;
-
-    this._maybePrepareForNewInitials(inputProps);
-
     const props: Record<string, unknown> = {};
     for (const key in inputProps) {
       const value = inputProps[key];
       if (key === 'style') {
         const styleProp = inputProps.style;
         const styles = flattenArray<StyleProps>(styleProp ?? []);
-        if (this._requiresNewInitials) {
-          this._initialStyle = {};
-        }
         const processedStyle: StyleProps = styles.map((style) => {
           if (style && style.viewDescriptors) {
             // this is how we recognize styles returned by useAnimatedStyle
-            if (this._requiresNewInitials) {
+            style.viewsRef?.add(component);
+            if (component._isFirstRender) {
               this._initialStyle = {
                 ...style.initial.value,
                 ...this._initialStyle,
@@ -56,7 +48,7 @@ export class PropsFilter implements IPropsFilter {
             }
             return this._initialStyle;
           } else if (hasInlineStyles(style)) {
-            return getInlineStyle(style, this._requiresNewInitials);
+            return getInlineStyle(style, component._isFirstRender);
           } else {
             return style;
           }
@@ -70,6 +62,7 @@ export class PropsFilter implements IPropsFilter {
           Object.keys(animatedProp.initial.value).forEach((initialValueKey) => {
             props[initialValueKey] =
               animatedProp.initial?.value[initialValueKey];
+            animatedProp.viewsRef?.add(component);
           });
         }
       } else if (
@@ -88,26 +81,13 @@ export class PropsFilter implements IPropsFilter {
           props[key] = dummyListener;
         }
       } else if (isSharedValue(value)) {
-        if (this._requiresNewInitials) {
+        if (component._isFirstRender) {
           props[key] = value.value;
         }
       } else if (key !== 'onGestureHandlerStateChange' || !isChromeDebugger()) {
         props[key] = value;
       }
     }
-    this._requiresNewInitials = false;
     return props;
   }
-
-  private _maybePrepareForNewInitials(
-    inputProps: AnimatedComponentProps<InitialComponentProps>
-  ) {
-    if (this._previousProps && inputProps.style) {
-      this._requiresNewInitials = !shallowEqual(
-        this._previousProps,
-        inputProps
-      );
-    }
-    this._previousProps = inputProps;
-  }
 }
