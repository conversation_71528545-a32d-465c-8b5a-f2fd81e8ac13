diff --git a/index.d.ts b/index.d.ts
index 0ce849f7cd5d373ff4c66d9e188255973982aa4c..180e8c5a339d4f6af0de7fe60bf2e46e4cd84f04 100644
--- a/index.d.ts
+++ b/index.d.ts
@@ -340,6 +340,12 @@ declare module "react-native-image-crop-picker" {
          * @default Android: 1, iOS: 0.8
          */
         compressImageQuality?: number;
+
+        /**
+         * An array with two entries `[x, y]` specifying the aspect ratio to maintain if the user is
+         * allowed to edit the image (by passing `cropping: true`).
+         */
+        aspect?: [number, number];
     }
 
     type CropperOptions = ImageOptions & {
diff --git a/ios/src/ImageCropPicker.m b/ios/src/ImageCropPicker.m
index 9f20973f1d641a8c3b96e825c1a4888eb80b55f9..cf4798321a721b876c4232d701ffed7d9bef4b8a 100644
--- a/ios/src/ImageCropPicker.m
+++ b/ios/src/ImageCropPicker.m
@@ -790,7 +790,15 @@ - (void)dismissCropper:(UIViewController *)controller selectionDone:(BOOL)select
             }
             break;
         case CAMERA:
-            [controller.presentingViewController.presentingViewController dismissViewControllerAnimated:YES completion:completion];
+            if(selectionDone) {
+                [controller.presentingViewController.presentingViewController dismissViewControllerAnimated:YES completion:completion];
+            } else {
+                // If a user opens the image picker, attempts to crop the image,
+                // but then cancels the cropping process, it would return app with reject
+                [controller.presentingViewController.presentingViewController dismissViewControllerAnimated:YES completion:[self waitAnimationEnd:^{
+                    self.reject(ERROR_PICKER_CANCEL_KEY, ERROR_PICKER_CANCEL_MSG, nil);
+                }]];
+            }
             break;
     }
 }
@@ -883,13 +891,24 @@ - (void)cropImage:(UIImage *)image {
             cropVC = [[TOCropViewController alloc] initWithCroppingStyle:TOCropViewCroppingStyleCircular image:image];
         } else {
             cropVC = [[TOCropViewController alloc] initWithImage:image];
-            CGFloat widthRatio = [[self.options objectForKey:@"width"] floatValue];
-            CGFloat heightRatio = [[self.options objectForKey:@"height"] floatValue];
-            if (widthRatio > 0 && heightRatio > 0){
-                CGSize aspectRatio = CGSizeMake(widthRatio, heightRatio);
-                cropVC.customAspectRatio = aspectRatio;
-                
+            
+            // 明确语义：width和height仅作为压缩比例
+            // 新增aspect指定裁剪窗口大小
+            id aspect = [self.options objectForKey:@"aspect"];
+            if ([aspect isKindOfClass:[NSArray class]] && [aspect count] >= 2) {
+                id width = aspect[0];
+                id height = aspect[1];
+                if ([width isKindOfClass:[NSNumber class]] && [height isKindOfClass:[NSNumber class]]) {
+                    CGFloat widthRatio = [width floatValue];
+                    CGFloat heightRatio = [height floatValue];
+                    
+                    if (widthRatio > 0 && heightRatio > 0){
+                      CGSize aspectRatio = CGSizeMake(widthRatio, heightRatio);
+                      cropVC.customAspectRatio = aspectRatio;
+                    }
+                }
             }
+          
             cropVC.aspectRatioLockEnabled = ![[self.options objectForKey:@"freeStyleCropEnabled"] boolValue];
             cropVC.resetAspectRatioEnabled = !cropVC.aspectRatioLockEnabled;
         }
