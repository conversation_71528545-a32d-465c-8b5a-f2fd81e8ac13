diff --git a/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm b/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm
index e3a7c6be60e..f7d062872a9 100644
--- a/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm
+++ b/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm
@@ -22,6 +22,10 @@
     _backedTextInputView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
     _backedTextInputView.textInputDelegate = self;
 
+    if (@available(iOS 17.0, *)) {
+      _backedTextInputView.inlinePredictionType = UITextInlinePredictionTypeNo;
+    }
+
     [self addSubview:_backedTextInputView];
   }
 
