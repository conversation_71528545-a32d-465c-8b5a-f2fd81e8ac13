diff --git a/android/build.gradle b/android/build.gradle
index a786d5e612e3e5b01ed52d44a19db864b67541be..651184bce787017e3db5036236982662759ac559 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -74,6 +74,7 @@ dependencies {
   implementation "com.google.firebase:firebase-messaging:${versions.firebaseMessaging}"
   implementation "com.twilio:audioswitch:${versions.audioSwitch}"
   implementation 'com.google.android.material:material:1.1.0'
+  implementation project(':expo-notifications')
 
   constraints {
     implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0") {
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java b/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java
index afb7cadfe106fdc1123257ce04352ded5b13e14f..8566477d50ed0ef775220be628381a29586bd19f 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java
@@ -40,7 +40,7 @@ public class VoiceActivityProxy {
     }
     // These flags ensure that the activity can be launched when the screen is locked.
     Window window = context.getWindow();
-    window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
+    window.clearFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
       | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
       | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
     // handle any incoming intents
@@ -81,7 +81,15 @@ public class VoiceActivityProxy {
       Intent copiedIntent = new Intent(intent);
       copiedIntent.setClass(context.getApplicationContext(), VoiceService.class);
       copiedIntent.setFlags(0);
-      context.getApplicationContext().startService(copiedIntent);
+
+      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && 
+          (action.equals(Constants.ACTION_RAISE_OUTGOING_CALL_NOTIFICATION) || 
+           action.equals(Constants.ACTION_ACCEPT_CALL))) {
+        // 针对 Android 12 及以上的来电和前台服务请求使用 startForegroundService
+        context.getApplicationContext().startForegroundService(copiedIntent);
+      } else {
+        context.getApplicationContext().startService(copiedIntent);
+      }
     }
   }
 
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java b/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java
index 042629f4c89bebef89485cc4d794066e4ae65cab..26c924351b19edba88242e71636b6e900e09b840 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java
@@ -12,7 +12,7 @@ import android.content.Intent;
 import android.content.ServiceConnection;
 import android.os.IBinder;
 
-import com.facebook.react.ReactNativeHost;
+import com.facebook.react.defaults.DefaultReactNativeHost;
 import com.facebook.react.ReactPackage;
 
 public class VoiceApplicationProxy {
@@ -27,15 +27,19 @@ public class VoiceApplicationProxy {
   private final ServiceConnection voiceServiceObserver = new ServiceConnection() {
     @Override
     public void onServiceConnected(ComponentName name, IBinder service) {
-      if (name.getClassName().equals(VoiceService.class.getName()))
-        voiceServiceApi = (VoiceService.VoiceServiceAPI)service;
+      try {
+        if (name.getClassName().equals(VoiceService.class.getName()))
+          voiceServiceApi = (VoiceService.VoiceServiceAPI)service;
+      } catch (Exception e) {
+        logger.error("Error binding to VoiceService: " + e.getMessage());
+      }
     }
     @Override
     public void onServiceDisconnected(ComponentName name) {
       voiceServiceApi = null;
     }
   };
-  public abstract static class VoiceReactNativeHost extends ReactNativeHost {
+  public abstract static class VoiceReactNativeHost extends DefaultReactNativeHost {
     public VoiceReactNativeHost(Application application) {
       super(application);
     }
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java b/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java
index ae9060e376d9758472ed0dbfd43b271bdeab371e..a5b9f7a1aa85f21e1ca788b34da147493a489bc0 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java
@@ -16,13 +16,26 @@ import com.twilio.voice.CallInvite;
 import com.twilio.voice.CancelledCallInvite;
 import com.twilio.voice.MessageListener;
 import com.twilio.voice.Voice;
-
+import expo.modules.notifications.service.delegates.FirebaseMessagingDelegate;
 import java.util.Objects;
 import java.util.UUID;
 
 public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
   private static final SDKLog logger = new SDKLog(VoiceFirebaseMessagingService.class);
 
+    private volatile FirebaseMessagingDelegate firebaseMessagingDelegate;
+
+    protected FirebaseMessagingDelegate getFirebaseMessagingDelegate() {
+        if(firebaseMessagingDelegate == null) {
+            synchronized (this) {
+                if (firebaseMessagingDelegate == null) {
+                    firebaseMessagingDelegate = new FirebaseMessagingDelegate(this);
+                }
+            }
+        }
+        return firebaseMessagingDelegate;
+    }
+
   public static class MessageHandler implements MessageListener  {
     @Override
     public void onCallInvite(@NonNull CallInvite callInvite) {
@@ -51,7 +64,12 @@ public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
   @Override
   public void onNewToken(@NonNull String token) {
     logger.log("Refreshed FCM token: " + token);
+      getFirebaseMessagingDelegate().onNewToken(token);
   }
+    @Override
+    public void onDeletedMessages() {
+        getFirebaseMessagingDelegate().onDeletedMessages();
+    }
 
   /**
    * Called when message is received.
@@ -80,9 +98,12 @@ public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
         remoteMessage.getData(),
         new MessageHandler(),
         new CallMessageListenerProxy())) {
-        logger.error("The message was not a valid Twilio Voice SDK payload: " +
+        logger.debug("The message was resolved by expo notification " +
           remoteMessage.getData());
+        getFirebaseMessagingDelegate().onMessageReceived(remoteMessage);
       }
     }
   }
+
+
 }
diff --git a/ios/TwilioVoiceReactNative+CallKit.m b/ios/TwilioVoiceReactNative+CallKit.m
index 65703842b60603edf0d416ae172bcf244e6fbfe2..882deb5e8326a027561a2ec778bef6bd9865ab03 100644
--- a/ios/TwilioVoiceReactNative+CallKit.m
+++ b/ios/TwilioVoiceReactNative+CallKit.m
@@ -173,9 +173,15 @@ - (void)performVoiceCallWithUUID:(NSUUID *)uuid
 
 - (void)performAnswerVoiceCallWithUUID:(NSUUID *)uuid
                             completion:(void(^)(BOOL success))completionHandler {
-    NSAssert(self.callInviteMap[uuid.UUIDString], @"No call invite");
+    // NSAssert(self.callInviteMap[uuid.UUIDString], @"No call invite");
     
     TVOCallInvite *callInvite = self.callInviteMap[uuid.UUIDString];
+
+    if (!callInvite) {
+        completionHandler(NO);
+        return;
+    }
+    
     TVOAcceptOptions *acceptOptions = [TVOAcceptOptions optionsWithCallInvite:callInvite block:^(TVOAcceptOptionsBuilder *builder) {
         builder.uuid = uuid;
         builder.callMessageDelegate = self;
