<svg width="85" height="93" viewBox="0 0 85 93" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.288 30C35.5295 30 30 34.3008 30 42.288C30 50.2752 42.288 62.768 42.288 62.768C42.288 62.768 54.5759 50.2752 54.5759 42.288C54.5759 34.3008 49.0464 30 42.288 30Z" fill="#29CD57"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.288 51.9817C36.8812 51.9817 32.4575 47.558 32.4575 42.1513C32.4575 36.7445 36.8812 32.321 42.288 32.321C47.6947 32.321 52.1184 36.7445 52.1184 42.1513C52.1184 47.558 47.6947 51.9817 42.288 51.9817Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.2878 47.1513C39.5379 47.1513 37.2878 44.9013 37.2878 42.1513C37.2878 39.4013 39.5379 37.1514 42.2878 37.1514C45.0379 37.1514 47.2878 39.4013 47.2878 42.1513C47.2878 44.9013 45.0379 47.1513 42.2878 47.1513Z" fill="#29CD57"/>
<defs>
<filter id="filter0_d" x="0" y="0" width="84.5759" height="92.768" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.297476 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
