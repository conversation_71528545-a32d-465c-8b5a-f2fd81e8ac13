<svg width="86" height="94" viewBox="0 0 86 94" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.788 30.5C36.0295 30.5 30.5 34.8008 30.5 42.788C30.5 50.7752 42.788 63.268 42.788 63.268C42.788 63.268 55.0759 50.7752 55.0759 42.788C55.0759 34.8008 49.5464 30.5 42.788 30.5Z" fill="black"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.788 52.4817C37.3812 52.4817 32.9575 48.058 32.9575 42.6513C32.9575 37.2445 37.3812 32.821 42.788 32.821C48.1947 32.821 52.6184 37.2445 52.6184 42.6513C52.6184 48.058 48.1947 52.4817 42.788 52.4817Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.7878 47.6513C40.0379 47.6513 37.7878 45.4013 37.7878 42.6513C37.7878 39.9013 40.0379 37.6514 42.7878 37.6514C45.5379 37.6514 47.7878 39.9013 47.7878 42.6513C47.7878 45.4013 45.5379 47.6513 42.7878 47.6513Z" fill="black"/>
<defs>
<filter id="filter0_d" x="0.5" y="0.5" width="84.5759" height="92.768" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.297476 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
