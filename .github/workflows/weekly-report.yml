name: Weekly Lint Report

on:
  schedule:
    - cron: "0 0 * * 1" # Every Monday at 8:00 AM Beijing Time
  workflow_dispatch:

jobs:
  run:
    uses: MoeGolibrary/moego-actions-tool/.github/workflows/weekly-report.yml@production
    with:
      project_name: "moego-mobile"
    secrets:
      NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
