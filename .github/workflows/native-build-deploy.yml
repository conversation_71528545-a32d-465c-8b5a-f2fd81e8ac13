name: Native Build and Deploy Workflow

on:
  workflow_dispatch:
    inputs:
      platform:
        description: 'Target platform (all/ios/android)'
        required: true
        type: choice
        options: 
          - all
          - ios
          - android

jobs:
  pre-check:
    runs-on: ubuntu-latest
    outputs:
      run_ios: ${{ steps.determine-build.outputs.run_ios }}
      run_android: ${{ steps.determine-build.outputs.run_android }}
    steps:
      - name: Determine Build Platforms 
        id: determine-build
        run: |
          if [ "${{ github.event.inputs.platform }}" = "all" ] || [ "${{ github.event.inputs.platform }}" = "ios" ]; then
            echo "run_ios=true" >> $GITHUB_OUTPUT
          else
            echo "run_ios=false" >> $GITHUB_OUTPUT
          fi
          if [ "${{ github.event.inputs.platform }}" = "all" ] || [ "${{ github.event.inputs.platform }}" = "android" ]; then
            echo "run_android=true" >> $GITHUB_OUTPUT
          else
            echo "run_android=false" >> $GITHUB_OUTPUT
          fi

  build:
    name: Build Job
    needs: pre-check
    strategy:
      matrix:
        platform: [ios, android]
        include:
          - platform: ios
            os: macos-latest
            condition: ${{ needs.pre-check.outputs.run_ios == 'true' }}
          - platform: android
            os: macos-latest
            condition: ${{ needs.pre-check.outputs.run_android == 'true' }}
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout moego-mobile Repository
        if: ${{ matrix.condition }}
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          lfs: true

      - name: App Pre Build Actions
        if: ${{ matrix.condition }}
        uses: ./.github/actions/app-pre-build
        with:
          NPM_PUBLISHER_USR: ${{ vars.NPM_PUBLISHER_USR }}
          NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}
          platform: ${{ matrix.platform }}  

      - name: App Build Actions
        if: ${{ matrix.condition }}
        uses: ./.github/actions/app-build
        with:
          configuration: ${{ env.configuration }}
          platform: ${{ matrix.platform }}  

      - name: App Post Build Actions
        if: ${{ matrix.condition }}
        uses: ./.github/actions/app-post-build
        with:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          configuration: ${{ env.configuration }}
          platform: ${{ matrix.platform }}  
