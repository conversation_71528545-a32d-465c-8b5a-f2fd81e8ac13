name: App Pre Build Actions
description: App Pre Build Actions

inputs:
  NPM_PUBLISHER_USR:
    description: 'npm publisher usr'
    required: true
  NPM_PUBLISHER_PSW:
    description: 'npm publisher psw'
    required: true
  platform:
    description: 'Target platform'
    required: true

runs:
  using: composite
  steps:
    - name: Configure Node.js Environment
      shell: bash
      run: |
        TOKEN=$(curl -s \
          -H "Accept: application/json" \
          -H "Content-Type:application/json" \
          -X PUT \
          -d '{"name": "${{ inputs.NPM_PUBLISHER_USR }}", "password": "${{ inputs.NPM_PUBLISHER_PSW }}"}' \
          https://nexus.devops.moego.pet/repository/npm-local/-/user/org.couchdb.user:${{ inputs.NPM_PUBLISHER_USR }} 2>&1 | jq -r .token)
        npm config set //nexus.devops.moego.pet/repository/npm-local/:_authToken $TOKEN
        npm config set registry 'https://registry.npmjs.org/'
        npm config set @moego:registry 'https://nexus.devops.moego.pet/repository/npm-local/'

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
          ruby-version: '3.2'
          bundler-cache: true 

    - name: Set up pnpm
      uses: pnpm/action-setup@v4

    - name: Set up Xcode
      if: ${{ inputs.platform == 'ios' }}
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable

    - name: Use Java 17
      if: ${{ inputs.platform == 'android' }} 
      shell: bash
      run: |
        export JAVA_HOME=$JAVA_HOME_17_X64
        echo "JAVA_HOME=$JAVA_HOME" >> $GITHUB_ENV
        java -version

    - name: Checkout app-ci-config-store Repository
      uses: actions/checkout@v4
      with:
        repository: MoeGolibrary/app-ci-config-store
        token: ****************************************
        path: app-ci-config-store
        fetch-depth: 1
        ref: feature-github-actions

    - name: Copy CI Configuration
      shell: bash
      run: cp -R app-ci-config-store/moego-mobile/. .

    - name: Install Bundle Dependencies
      shell: bash
      run: |
        bundle install

    # --- 环境变量设置 ---
    - name: Set Dynamic Environment
      id: set_dynamic_env
      shell: bash
      run: |
        echo "Current branch: ${{ github.ref_name }}"
        configuration=test
        if [[ "${{ github.ref_name }}" == testflight-* ]]; then
          configuration=production
        fi
        echo "configuration=$configuration" >> $GITHUB_ENV

        if [ -f ./build_env ]; then
          source ./build_env 
          echo "MATCH_GIT_BASIC_AUTHORIZATION=$MATCH_GIT_BASIC_AUTHORIZATION" >> $GITHUB_ENV
          echo "MATCH_PASSWORD=$MATCH_PASSWORD" >> $GITHUB_ENV
          echo "VERSION_NUMBER=$VERSION_NUMBER" >> $GITHUB_ENV
          echo "FASTLANE_USER=$FASTLANE_USER" >> $GITHUB_ENV
          echo "FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=$FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD" >> $GITHUB_ENV
        fi

    - name: Run delete_expo_updates_patch (Production Only)
      if: ${{ env.configuration == 'production' }}
      shell: bash
      run: |
        python3 -m venv .venv
        source .venv/bin/activate
        pip install ruamel.yaml
        python3 delete_expo_updates_patch.py

    - name: Setup CocoaPods Cache
      if: ${{ inputs.platform == 'ios' }}
      uses: actions/cache@v4
      with:
        path: ~/Library/Caches/CocoaPods
        key: ${{ runner.os }}-cocoapods-downloads-${{ hashFiles('ios/Podfile.lock') }}
        restore-keys: |
          ${{ runner.os }}-cocoapods-downloads-

    - name: Setup Gradle Cache
      if: ${{ inputs.platform == 'android' }} 
      uses: gradle/actions/setup-gradle@v4

    # --- 依赖安装 ---
    - name: Install pnpm Dependencies
      shell: bash
      run: |
        pnpm install --frozen-lockfile > /dev/null
        echo "Exit code: $?"

    - name: Install CocoaPods Dependencies
      if: ${{ inputs.platform == 'ios' }}
      shell: bash
      run: |
        cd ios
        pod install > /dev/null
        cd ..

    - name: Install Android Dependencies
      if: ${{ inputs.platform == 'android' }} 
      shell: bash
      run: |
        cd android
        ./gradlew dependencies > /dev/null
        cd ..