name: App Post Build Actions
description: App Post Build Actions

inputs:
  AWS_ACCESS_KEY_ID:
    description: 'aws access key id'
    required: true
  AWS_SECRET_ACCESS_KEY:
    description: 'aws secret access key'
    required: true
  configuration:
    description: 'configuration'
    required: true
  platform:
    description: 'Target platform'
    required: true

runs:
  using: composite
  steps:
    - name: Upload dSYM and DIF Files
      shell: bash
      run: |
        # iOS平台处理dSYM
        if [[ "${{ inputs.platform }}" == "ios" ]]; then
          ios_dsym_path="ios/build/moego-business-mobile.app.dSYM.zip"
          if [ -f "$ios_dsym_path" ]; then
            echo "Uploading iOS dSYM file..."
            export SENTRY_PROPERTIES=./ios/sentry.properties
            ./node_modules/.bin/sentry-cli upload-dsym $ios_dsym_path
          fi
        # Android平台处理DIF
        elif [[ "${{ inputs.platform }}" == "android" ]]; then
          android_dif_path="android/app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib/arm64-v8a"
          if [ -d "$android_dif_path" ]; then
            echo "Uploading Android DIF..."
            export SENTRY_PROPERTIES=./android/sentry.properties
            ./node_modules/@sentry/cli/bin/sentry-cli upload-dif $android_dif_path
          fi
        fi

    # --- 部署与上传 ---
    - name: Configure AWS Credentials (Test Only)
      if: ${{ inputs.configuration == 'test' }}
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ inputs.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2

    - name: Upload Test Packages (Test Only)
      if: ${{ inputs.configuration == 'test' }}
      shell: bash
      run: |
        npx tsx ./scripts/upload_adhoc.ts
        echo "https://moego.s3-us-west-2.amazonaws.com/Public/Assets/moego-business-mobile/${{ github.ref_name }}/moego-business-mobile.html"

    - name: Upload Production Packages (Production Only)
      if: ${{ inputs.configuration == 'production' }}
      shell: bash
      run: |
        # 根据平台执行对应上传
        if [[ "${{ inputs.platform }}" == "ios" ]]; then
          if [ -f "ios/build/moego-business-mobile.ipa" ]; then
            echo "Uploading iOS IPA..."
            fastlane ios upload
            echo "IOS_UPLOADED=true" >> $GITHUB_ENV
          fi
        elif [[ "${{ inputs.platform }}" == "android" ]]; then
          if [ -f "android/app/build/outputs/bundle/release/app-release.aab" ]; then
            echo "Uploading Android AAB..."
            fastlane android upload
            echo "ANDROID_UPLOADED=true" >> $GITHUB_ENV
          fi
        fi

    - name: Log to Summary
      shell: bash
      run: |
        echo "| 类型 | 链接 |" >> $GITHUB_STEP_SUMMARY
        echo "|------|------|" >> $GITHUB_STEP_SUMMARY
        if [[ ${{ inputs.configuration }} == 'test' ]]; then
          echo "| AWS | https://moego.s3-us-west-2.amazonaws.com/Public/Assets/moego-business-mobile/${{ github.ref_name }}/moego-business-mobile.html |" >> $GITHUB_STEP_SUMMARY
        else
          if [ "${{ env.IOS_UPLOADED }}" = "true" ]; then 
            echo "| testflight | https://appstoreconnect.apple.com/teams/69a6de8f-d0b1-47e3-e053-5b8c7c11a4d1/apps/1561621817/testflight/ios |" >> $GITHUB_STEP_SUMMARY
          fi
          if [ "${{ env.ANDROID_UPLOADED }}" = "true" ]; then
            echo "| playconsole | https://play.google.com/console/u/0/developers/6175666307382211734/app/4975836246616582765/tracks/internal-testing |" >> $GITHUB_STEP_SUMMARY
          fi
        fi