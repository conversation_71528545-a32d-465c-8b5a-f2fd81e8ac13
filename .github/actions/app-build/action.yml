name: App Pre Build Actions
description: App Pre Build Actions

inputs:
  configuration:
    description: 'configuration'
    required: true
  platform:
    description: 'Target platform'
    required: true
runs:
  using: composite
  steps:
    - name: Build Project
      shell: bash
      run: |
        pnpm native:preassemble --env $configuration
        LANE="build_$configuration"
        
        if [[ "${{ inputs.platform }}" == 'ios' ]]; then
          echo "fastlane ios $LANE"
          bundle exec fastlane ios $LANE > /dev/null
        elif [[ "${{ inputs.platform }}" == 'android' ]]; then
          echo "fastlane android $LANE"
          bundle exec fastlane android $LANE -- \
            -x lintVitalRelease \
            -PreactNativeArchitectures=armeabi-v7a,arm64-v8a > /dev/null
        fi

   