package com.moego.server.customer.service.tmp;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Env;
import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.ContentCachingRequestWrapper;

@Component
public class RequestForwarder {

    private final RestTemplate restTemplate;

    private static final String GV_SERVER_CUSTOMER_HEADER = "gv-moego-service-customer";
    private static final String GV_SVC_BUSINESS_CUSTOMER_HEADER = "gv-moego-svc-business-customer";
    private static final String GV_MOEGO_CUSTOMER_HEADER = "gv-moego-customer";
    private static final String REFACTOR_HEADER = "x-moe-customer-refactor";

    private final Environment env;

    public RequestForwarder(Environment env) {
        this.env = env;
        this.restTemplate = new RestTemplate();
    }

    /**
     * 通用请求转发方法
     * @param request 原始请求
     * @param responseType 响应类型
     * @return 转发后的响应
     */
    public <T> T forward(HttpServletRequest request, Class<T> responseType) {

        if (isInLoop(request)) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "reject loop request");
        }

        String targetBaseUrl = "http://moego-service-customer:9201";
        Map<String, String> customHeaders = Map.of(
                GV_SERVER_CUSTOMER_HEADER, "feature-customer-refactor",
                GV_MOEGO_CUSTOMER_HEADER, "feature-customer-refactor",
                GV_SVC_BUSINESS_CUSTOMER_HEADER, "feature-customer-refactor",
                REFACTOR_HEADER, "1");
        try {
            // 构建完整URL（包含path和query parameters）
            String fullUrl = buildFullUrl(targetBaseUrl, request);

            // 创建headers
            HttpHeaders headers = new HttpHeaders();
            copyOriginalHeaders(request, headers);
            customHeaders.forEach(headers::set);

            // 获取请求体（如果有）
            String requestBody = getRequestBody(request);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // 转发请求
            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            ResponseEntity<T> response = restTemplate.exchange(fullUrl, method, entity, responseType);

            return response.getBody();
        } catch (Exception e) {
            throw new RuntimeException("Request forward failed", e);
        }
    }

    public <T> T forward(HttpServletRequest request, ParameterizedTypeReference<T> responseType) {
        String targetBaseUrl = "http://moego-service-customer:9201";
        Map<String, String> customHeaders = Map.of(
                GV_SERVER_CUSTOMER_HEADER, "feature-customer-refactor",
                GV_MOEGO_CUSTOMER_HEADER, "feature-customer-refactor",
                GV_SVC_BUSINESS_CUSTOMER_HEADER, "feature-customer-refactor",
                REFACTOR_HEADER, "1");
        try {
            String fullUrl = buildFullUrl(targetBaseUrl, request);

            HttpHeaders headers = new HttpHeaders();
            copyOriginalHeaders(request, headers);
            customHeaders.forEach(headers::set);

            String requestBody = getRequestBody(request);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            ResponseEntity<T> response = restTemplate.exchange(fullUrl, method, entity, responseType);

            return response.getBody();
        } catch (Exception e) {
            throw new RuntimeException("Request forward failed", e);
        }
    }

    private String buildFullUrl(String baseUrl, HttpServletRequest request) {
        StringBuilder url = new StringBuilder(baseUrl);

        // 添加路径
        String pathInfo = request.getRequestURI();
        if (pathInfo != null && !pathInfo.equals("/")) {
            if (!baseUrl.endsWith("/") && !pathInfo.startsWith("/")) {
                url.append("/");
            }
            url.append(pathInfo);
        }

        // 添加查询参数
        String queryString = request.getQueryString();
        if (queryString != null) {
            url.append("?").append(queryString);
        }

        return url.toString();
    }

    private void copyOriginalHeaders(HttpServletRequest request, HttpHeaders headers) {
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.set(headerName, headerValue);
        }
    }

    private String getRequestBody(HttpServletRequest request) {
        try {
            if ("GET".equalsIgnoreCase(request.getMethod()) || "DELETE".equalsIgnoreCase(request.getMethod())) {
                return null;
            }

            if (request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                return new String(wrapper.getContentAsByteArray(), StandardCharsets.UTF_8);
            }

            StringBuilder body = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return !body.isEmpty() ? body.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    private boolean isInLoop(HttpServletRequest request) {
        if (env.matchesProfiles(Env.TEST2.getValue()) || request == null) {
            return false;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (headerName.equals(GV_SERVER_CUSTOMER_HEADER)
                    || headerName.equals(GV_MOEGO_CUSTOMER_HEADER)
                    || headerName.equals(GV_SVC_BUSINESS_CUSTOMER_HEADER)
                    || headerName.equals(REFACTOR_HEADER)) {
                return true;
            }
        }
        return false;
    }
}
