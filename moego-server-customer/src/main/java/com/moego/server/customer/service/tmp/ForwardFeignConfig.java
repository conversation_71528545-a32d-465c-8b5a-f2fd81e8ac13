package com.moego.server.customer.service.tmp;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Env;
import feign.RequestInterceptor;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Configuration
public class ForwardFeignConfig {

    private static final String GV_SERVER_CUSTOMER_HEADER = "gv-moego-service-customer";
    private static final String GV_SVC_BUSINESS_CUSTOMER_HEADER = "gv-moego-svc-business-customer";
    private static final String GV_MOEGO_CUSTOMER_HEADER = "gv-moego-customer";
    private static final String REFACTOR_HEADER = "x-moe-customer-refactor";
    private final Environment env;

    public ForwardFeignConfig(Environment env) {
        this.env = env;
    }

    @Bean
    @Primary // 如果有多个RequestInterceptor
    public RequestInterceptor forwardRequestInterceptor() {
        return requestTemplate -> {
            // 获取当前HTTP请求
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (!(requestAttributes instanceof ServletRequestAttributes)) {
                return;
            }

            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();

            if (isInLoop(request)) {
                throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "reject loop request");
            }

            // 复制原始请求的headers
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                if (shouldSkipHeader(headerName)) {
                    continue;
                }
                requestTemplate.header(headerName, headerValue);
            }

            // 添加转发标识
            requestTemplate.header(GV_SERVER_CUSTOMER_HEADER, "feature-customer-refactor");
            requestTemplate.header(GV_SVC_BUSINESS_CUSTOMER_HEADER, "feature-customer-refactor");
            requestTemplate.header(GV_MOEGO_CUSTOMER_HEADER, "feature-customer-refactor");
            requestTemplate.header(REFACTOR_HEADER, "1");
        };
    }

    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host")
                || lowerName.equals("content-length")
                || lowerName.startsWith("accept-encoding");
    }

    private boolean isInLoop(HttpServletRequest request) {
        if (env.matchesProfiles(Env.TEST2.getValue()) || request == null) {
            return false;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (headerName.equals(GV_SERVER_CUSTOMER_HEADER)
                    || headerName.equals(GV_MOEGO_CUSTOMER_HEADER)
                    || headerName.equals(GV_SVC_BUSINESS_CUSTOMER_HEADER)
                    || headerName.equals(REFACTOR_HEADER)) {
                return true;
            }
        }
        return false;
    }
}
