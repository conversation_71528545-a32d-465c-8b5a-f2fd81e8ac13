{
  "typescript.tsdk": "node_modules/typescript/lib",
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "package.json": "pnpm*, .npm*, turbo.json, tsconfig*.json, *.json, nginx.conf",
    "README.md": "*.md",
    ".editorconfig": ".git*, .env*, .dockerignore, .textlintrc, .prettierignore",
    "app.config.ts": "*.config.js, *.config.ts, *.config.mjs, jest.coverage.js",
  },
  "editor.formatOnSave": true,
  "biome.enabled": false,
}
