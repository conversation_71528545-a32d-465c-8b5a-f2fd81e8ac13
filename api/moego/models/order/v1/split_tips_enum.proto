syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// split tips method
enum SplitTipsMethod {
  // split tips method unspecified
  SPLIT_TIPS_METHOD_UNSPECIFIED = 0;
  // split tips method by service
  SPLIT_TIPS_METHOD_BY_SERVICE = 1;
  // split tips method by equally
  SPLIT_TIPS_METHOD_BY_EQUALLY = 2;
  // deprecated split tips method customized
  SPLIT_TIPS_METHOD_CUSTOMIZED = 3;
  // split tips method by percentage
  SPLIT_TIPS_METHOD_BY_PERCENTAGE = 4;
  // split tips method by fixed amount
  SPLIT_TIPS_METHOD_BY_FIXED_AMOUNT = 5;
}

// customized tip type
enum CustomizedTipType {
  // customized tip type unspecified
  CUSTOMIZED_TIP_TYPE_UNSPECIFIED = 0;
  // customized tip type amount
  CUSTOMIZED_TIP_TYPE_AMOUNT = 1;
  // customized tip type percentage
  CUSTOMIZED_TIP_TYPE_PERCENTAGE = 2;
}

// tips split mode
enum TipsSplitMode {
  // 未指定模式.
  TIPS_SPLIT_MODE_UNSPECIFIED = 0;
  // tips split by appt
  TIPS_SPLIT_MODE_APPT = 1;
  // tips split by order
  TIPS_SPLIT_MODE_ORDER = 2;
}
