syntax = "proto3";

package moego.models.order.v1;

import "google/protobuf/timestamp.proto";
import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/split_tips_defs.proto";
import "moego/models/order/v1/split_tips_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// split tips record
message SplitTipsRecord {
  // record id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // order id
  int64 order_id = 3;
  // split method
  SplitTipsMethod split_method = 4;
  // customized type: amount/percentage
  CustomizedTipType customized_type = 5;
  // customize config
  repeated CustomizedTipConfig customized_config = 6;
  // apply by
  int64 apply_by = 7;
  // create time
  google.protobuf.Timestamp create_time = 8;
  // update time
  google.protobuf.Timestamp update_time = 9;
  // 表示 business tip amount 是否为定制数据.
  // 仅当该字段为 True 时，才应当使用 BusinessTipAmount 字段.
  bool is_business_tip_amount_effective = 10;
  // tips to business.
  google.type.Decimal business_tip_amount = 11;
}

// tips split model
message TipsSplitModel {
  // record id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 7;
  // source id
  int64 source_id = 3;
  // source type
  models.order.v1.OrderSourceType source_type = 4;
  // split config
  TipsSplitConfig split_config = 5;
  // apply by (the staff id)
  int64 apply_by = 8;
  // tips collected amount
  google.type.Money collected_tips = 9;
  // collected order ids
  repeated int64 collected_order_ids = 11;
  // create time
  google.protobuf.Timestamp create_time = 12;
  // update time
  google.protobuf.Timestamp update_time = 13;

  // tips split config
  message TipsSplitConfig {
    // split method
    SplitTipsMethod split_method = 1;
    // staff configs
    repeated StaffTipConfig staff_configs = 2;
    // Business tip amount.
    optional google.type.Money business_tip_amount = 3;
  }
}

// tips split detail view
message TipsSplitDetailView {
  // tips split detail model
  TipsSplitDetailModel tips_split_detail = 1;
  // 关联的 Staff 的基本信息.
  OrderDetailView.StaffBrief staff_brief = 2;
}

// tips split detail model
message TipsSplitDetailModel {
  // record id
  int64 id = 1;
  // tips split primary id
  int64 tips_split_id = 2;
  // business id
  int64 business_id = 3;
  // company id
  int64 company_id = 8;
  // staff id
  int64 staff_id = 4;
  // split amount
  google.type.Money split_amount = 5;
  // create time
  google.protobuf.Timestamp create_time = 6;
  // update time
  google.protobuf.Timestamp update_time = 7;
}
