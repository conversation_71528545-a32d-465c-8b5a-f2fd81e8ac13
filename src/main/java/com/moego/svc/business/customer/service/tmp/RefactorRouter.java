package com.moego.svc.business.customer.service.tmp;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcRequestContext;
import com.moego.lib.common.util.Env;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.svc.business.customer.repo.CustomerContactRepo;
import com.moego.svc.business.customer.repo.CustomerNoteRepo;
import com.moego.svc.business.customer.repo.CustomerRepo;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeBusinessCustomerRecord;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerAddressRecord;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerContactRecord;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerSourceRecord;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerTagRecord;
import com.moego.svc.business.customer.service.CustomerAddressService;
import com.moego.svc.business.customer.service.CustomerReferralSourceService;
import com.moego.svc.business.customer.service.CustomerTagService;
import io.grpc.ClientInterceptor;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@AllArgsConstructor
public class RefactorRouter {

    private final FeatureFlagApi featureFlagApi;
    private final CustomerRepo customerRepo;
    private final CustomerAddressService customerAddressService;
    private final CustomerContactRepo customerContactRepo;
    private final CustomerNoteRepo customerNoteRepo;
    private final CustomerReferralSourceService customerReferralSourceService;
    private final CustomerTagService customerTagService;
    private final Environment env;

    private static final String GV_SVC_BUSINESS_CUSTOMER_HEADER = "gv-moego-svc-business-customer";
    private static final String GV_SERVER_CUSTOMER_HEADER = "gv-moego-service-customer";
    private static final String GV_MOEGO_CUSTOMER_HEADER = "gv-moego-customer";
    private static final String REFACTOR_HEADER = "x-moe-customer-refactor";

    private static final Integer PG_CUSTOMER_OFFSET = *********;

    public boolean needForwardByCustomer(Long customerId) {
        if (customerId >= PG_CUSTOMER_OFFSET) {
            return true;
        }
        var entity = customerRepo.getCustomerInfo(null, customerId);
        if (Objects.isNull(entity)) {
            return false;
        }
        var companyID = entity.getCompanyId();
        if (Objects.isNull(companyID) || companyID <= 0) {
            return false;
        }
        return needForwardByCompany(companyID);
    }

    public boolean needForwardByCustomers(List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return false;
        }

        if (customerIds.stream().anyMatch((id -> id > PG_CUSTOMER_OFFSET))) {
            return true;
        }

        var entities = customerRepo.batchGetCustomerInfo(null, customerIds);
        if (CollectionUtils.isEmpty(entities)) {
            return false;
        }

        var companyIds = entities.stream()
                .map(MoeBusinessCustomerRecord::getCompanyId)
                .filter(Objects::nonNull)
                .filter(i -> i > 0)
                .distinct()
                .toList();

        return needForwardByCompanies(companyIds);
    }

    public boolean needForwardByAddress(Long addressId) {
        if (addressId >= PG_CUSTOMER_OFFSET) {
            return true;
        }
        var address = customerAddressService.getCustomerAddress(null, addressId, true);
        if (Objects.isNull(address)) {
            return false;
        }
        return needForwardByCompany(address.getCompanyId());
    }

    public boolean needForwardByAddresses(List<Long> addressIds) {
        if (CollectionUtils.isEmpty(addressIds)) {
            return false;
        }

        if (addressIds.stream().anyMatch((id -> id > PG_CUSTOMER_OFFSET))) {
            return true;
        }
        var addresses = customerAddressService.batchGetCustomerAddress(null, addressIds);
        if (CollectionUtils.isEmpty(addresses)) {
            return false;
        }

        var companyIds = addresses.stream()
                .map(MoeCustomerAddressRecord::getCompanyId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        return needForwardByCompanies(companyIds);
    }

    public boolean needForwardByContacts(List<Long> contactIds) {
        if (CollectionUtils.isEmpty(contactIds)) {
            return false;
        }

        if (contactIds.stream().anyMatch((id -> id > PG_CUSTOMER_OFFSET))) {
            return true;
        }
        var contacts = customerContactRepo.batchGetCustomerContact(contactIds);
        if (CollectionUtils.isEmpty(contacts)) {
            return false;
        }

        var companyIds = contacts.stream()
                .map(MoeCustomerContactRecord::getCompanyId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        return needForwardByCompanies(companyIds);
    }

    public boolean needForwardByNotes(List<Long> noteIds) {
        if (CollectionUtils.isEmpty(noteIds)) {
            return false;
        }

        if (noteIds.stream().anyMatch((id -> id > PG_CUSTOMER_OFFSET))) {
            return true;
        }
        var notes = customerNoteRepo.batchGetCustomerNote(noteIds);
        if (CollectionUtils.isEmpty(notes)) {
            return false;
        }

        var customerIds = notes.stream()
                .map(x -> x.getCustomerId().longValue())
                .filter(id -> id > 0)
                .distinct()
                .toList();
        return needForwardByCustomers(customerIds);
    }

    public boolean needForwardBySources(List<Long> sourceIds) {
        if (CollectionUtils.isEmpty(sourceIds)) {
            return false;
        }

        if (sourceIds.stream().anyMatch((id -> id > PG_CUSTOMER_OFFSET))) {
            return true;
        }
        var sources = customerReferralSourceService.batchGetCustomerSource(sourceIds);
        if (CollectionUtils.isEmpty(sources)) {
            return false;
        }

        var companyIds = sources.stream()
                .map(MoeCustomerSourceRecord::getCompanyId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        return needForwardByCompanies(companyIds);
    }

    public boolean needForwardByTags(List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            return false;
        }

        if (tagIds.stream().anyMatch((id -> id > PG_CUSTOMER_OFFSET))) {
            return true;
        }
        var tags = customerTagService.batchGetCustomerTag(tagIds);
        if (CollectionUtils.isEmpty(tags)) {
            return false;
        }

        var companyIds = tags.stream()
                .map(MoeCustomerTagRecord::getCompanyId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        return needForwardByCompanies(companyIds);
    }

    public boolean needForwardByCompany(Long companyId) {
        if (Objects.isNull(companyId) || companyId <= 0) {
            return false;
        }
        return featureFlagApi.isOn(
                FeatureFlags.REFACTOR_CUSTOMER_WHITELIST,
                FeatureFlagContext.builder().company(companyId).build());
    }

    public boolean needForwardByCompanies(List<Long> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return false;
        }
        return companyIds.stream().anyMatch((this::needForwardByCompany));
    }

    public ClientInterceptor getInjectedInterceptor() {
        if (isInLoop()) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "reject loop request");
        }

        Metadata metadata = new Metadata();
        Metadata.Key<String> gv = Metadata.Key.of(GV_SVC_BUSINESS_CUSTOMER_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        metadata.put(gv, "feature-customer-refactor");
        Metadata.Key<String> gv2 = Metadata.Key.of(GV_SERVER_CUSTOMER_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        metadata.put(gv2, "feature-customer-refactor");
        Metadata.Key<String> gv3 = Metadata.Key.of(GV_MOEGO_CUSTOMER_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        metadata.put(gv3, "feature-customer-refactor");
        Metadata.Key<String> refactorAuth = Metadata.Key.of(REFACTOR_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        metadata.put(refactorAuth, "1");
        return MetadataUtils.newAttachHeadersInterceptor(metadata);
    }

    private boolean isInLoop() {

        if (env.matchesProfiles(Env.TEST2.getValue())) {
            return false;
        }

        var ctx = GrpcRequestContext.get();
        if (Objects.isNull(ctx)) {
            return false;
        }
        var headers = ctx.headers();
        if (Objects.isNull(headers)) {
            return false;
        }

        var keyGv = Metadata.Key.of(GV_SVC_BUSINESS_CUSTOMER_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        var keyGv2 = Metadata.Key.of(GV_SERVER_CUSTOMER_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        var keyGv3 = Metadata.Key.of(GV_MOEGO_CUSTOMER_HEADER, Metadata.ASCII_STRING_MARSHALLER);
        var keyRefactor = Metadata.Key.of(REFACTOR_HEADER, Metadata.ASCII_STRING_MARSHALLER);

        return headers.containsKey(keyGv)
                || headers.containsKey(keyGv2)
                || headers.containsKey(keyGv3)
                || headers.containsKey(keyRefactor);
    }
}
