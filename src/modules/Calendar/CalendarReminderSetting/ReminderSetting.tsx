import React, { useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import IconIcDropdownBlackSvg from '../../../../assets/images/ic-dropdown-black.svg';
import { MoeText } from '../../../components/MoeText';
import { Select } from '../../../components/selects/Select';
import { createLazyStackScreen } from '../../../utils/createScreen';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useLoad } from '../../../utils/hooks/useLoad';
import { useThunkDispatch } from '../../../utils/store/action';
import { useListSelector } from '../../../utils/store/selector';
import { VS_FULL, VS_INPUT_CONTENT, VS_ROW_BETWEEN } from '../../../utils/style/misc';
import { PT_20 } from '../../../utils/style/preset/padding';
import { PATH_CALENDAR_REMINDER_SETTING } from '../Calendar.api';
import {
  getCalendarReminderSetings,
  updateCalendarSettings,
} from '../store/actions/private/calendar_reminder_setting.actions';
import { CalendarReminderBeforeTime, CalendarReminderStatus } from '../store/calendar_reminder_setting.boxes';
import { selectCalendarReminderSettings } from '../store/calendar_reminder_setting.selectors';

export const CalendarReminderSetting = createLazyStackScreen<
  Parameters<(typeof PATH_CALENDAR_REMINDER_SETTING)['push']>[0]
>((_, navigation, route) => {
  const dispatch = useThunkDispatch();
  const load = useLoad(() => dispatch(getCalendarReminderSetings()));
  const [setting] = useListSelector(selectCalendarReminderSettings());

  const options = useMemo(
    () => [
      ...CalendarReminderBeforeTime.map((key) => ({
        value: key,
        label: CalendarReminderBeforeTime.label(key),
      })),
      {
        value: CalendarReminderStatus.Off,
        label: 'Off',
      },
    ],
    [],
  );

  const selectValue = useMemo(() => {
    if (setting.status === CalendarReminderStatus.Off) {
      return CalendarReminderStatus.Off;
    }
    return setting.beforeTime;
  }, [setting]);

  const handleUpdate = useLatestCallback((val) => {
    const newSetting = {
      status: val === CalendarReminderStatus.Off ? CalendarReminderStatus.Off : CalendarReminderStatus.On,
      beforeTime: val === CalendarReminderStatus.Off ? 0 : val,
    };

    dispatch(updateCalendarSettings(newSetting));
  });

  return (
    <ScrollView
      style={[VS_FULL]}
      contentContainerStyle={[VS_INPUT_CONTENT, PT_20]}
      refreshControl={load.control(setting.staffId)}
    >
      <Select
        options={options}
        value={selectValue}
        onChangeText={handleUpdate}
        render={(text) => {
          return (
            <View style={[VS_ROW_BETWEEN]}>
              <MoeText>Appointment notification</MoeText>
              <View style={[VS_FULL]} />
              <>
                <MoeText>{text}</MoeText>
                <IconIcDropdownBlackSvg width={16} height={16} />
              </>
            </View>
          );
        }}
      />
    </ScrollView>
  );
});

export default CalendarReminderSetting;
