/*
 * @Author: <EMAIL>
 */

import { Dayjs } from 'dayjs';
import hexToRgba from 'hex-to-rgba';
import React, { memo, ReactNode, RefObject, useState } from 'react';
import {
  ActivityIndicator,
  Animated,
  DimensionValue,
  PanResponder,
  PanResponderInstance,
  StyleProp,
  StyleSheet,
  TextInput,
  TextStyle,
  TouchableOpacity,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native';
import IconAutoAssignWhiteSvg from '../../../../../assets/images/auto-assign-white.svg';
import IconIcCarOrange2 from '../../../../../assets/images/ic-car-orange-2.svg';
import IconIcRepeatGreenSvg from '../../../../../assets/images/ic-repeat-green.svg';
import IconIcWarningTriangleSvg from '../../../../../assets/images/ic-warning-triangle.svg';
import IconIcWarningSvg from '../../../../../assets/images/ic-warning.svg';
import { Dash } from '../../../../components/Dash/Dash';
import { MoeInput } from '../../../../components/inputs/MoeInput';
import { Full } from '../../../../components/layouts/Full';
import { MoeTag } from '../../../../components/MoeTag';
import { MoeText } from '../../../../components/MoeText';
import { gotIt } from '../../../../utils/alert';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { Haptics } from '../../../../utils/NativeModules';
import { useListSelector } from '../../../../utils/store/selector';
import { COL_BLACK, COL_E0, COL_GRAY, COL_ORANGE, COL_WHITE } from '../../../../utils/style/consts';
import { VS_SHRINK } from '../../../../utils/style/misc';
import { H_20 } from '../../../../utils/style/preset/height';
import { ML_2, ML_4, ML_6, MR_4 } from '../../../../utils/style/preset/margin';
import { PH_6 } from '../../../../utils/style/preset/padding';
import {
  TS_10_WHITE,
  TS_12,
  TS_12_WHITE,
  TS_14,
  TS_14_DARK,
  TS_14_WHITE,
  TS_16_ORANGE,
} from '../../../../utils/style/preset/text';
import { BusinessType } from '../../../Business/store/business.boxes';
import { selectBusiness } from '../../../Business/store/business.selectors';
import { PRE_AUTH_HL_STYLE } from '../../../Grooming/components/PreAuth/PreAuth.styles';
import { PreAuthStatusIcon } from '../../../Grooming/components/PreAuth/PreAuthStatusIcon';
import { judgeTicketWithBDService } from '../../../Grooming/GroomingTicketDetail/hooks/useMultiServicesRender';
import { getContrastTxtColor } from '../../../OnlineBooking/NewOnlineBookingSetting/hooks/useThemeColor';
import { useInvoiceReinvent } from '../../../Payment/Order/hooks/useInvoiceReinvent';
import { preAuthDetailMapBox } from '../../../Payment/store/preAuth.boxes';
import { selectCurrentPermissions } from '../../../Staff/store/role.selectors';
import { staffMapBox } from '../../../Staff/store/staff.boxes';
import { ShowColorCodeWithType } from '../../CalendarConfiguration/CalendarConfiguration';
import { useDrivingTimeForCardView } from '../../DrivingTimeInfo/useDrivingTimeForCardView';
import { groomingCardStatusColorMap } from '../../store/calendar.utils';
import { CalendarGroomingTicketRecord, CardStatus, DrivingDisplayInfo } from '../../store/calendar_data.boxes';
import { selectCalendarSettings } from '../../store/calendar_settings.selectors';
import { getBlockXDayStartAndEnd } from '../../store/utils';
import { CellHeight } from '../CalendarDailyView/CalendarDailyView.styles';
import { PetRow } from '../PetRow/PetRow';
import { DriveInIndicatorViewMinHeight, HeaderHeight, styles } from './CalendarCard.styles';

interface Props {
  cellHeight: number;
  disableDragging?: boolean;
  sourceCard: CalendarGroomingTicketRecord;
  prevSourceCard?: CalendarGroomingTicketRecord;
  isMultiStaff: boolean;
  layout: {
    agendaCellPaddingLeft: number;
    agendaCellPaddingRight: number;
    // screenWidth - HourCellWidth
    appointmentsViewWidth: number;
    // agenda for certain user
    agendaCellWidth: number;
    cardMaximumWidth: number;
  };
  highlight?: boolean;
  showServiceInfo?: boolean;
  showDrivingTimeInfo?: boolean;
  drivingTimeDisplayInfo?: DrivingDisplayInfo;
  isDriveFromStart?: boolean;
  onPress(): void;
  onLongPress(): void;
  onDrag?({}: {
    touchCurrentX: number;
    touchCurrentY: number;
    touchBaseX: number;
    touchBaseY: number;
    moveCardBasedX: number;
    cardLeft: number;
    cardTop: number;
    touchLocationX: number;
    touchLocationY: number;
    touchBaseLocationX: number;
    touchBaseLocationY: number;
  }): void;
  onDragEnd?(): void;
  opacity: Animated.Value | Animated.AnimatedInterpolation<number>;
  showLoading?: boolean;
  rescheduleProps?: RescheduleCardProps;
  hourTextRef?: RefObject<TextInput>;
  cardRef?: RefObject<TouchableOpacity>;
}

export interface RescheduleCardProps {
  width: number;
  height: number;
  top: number;
  left: number;
  startTime: Dayjs;
  duration: number;
}

const numberOfLines: number = 1;

export const CalendarCard = memo<Props>(
  ({
    cellHeight,
    disableDragging = false,
    sourceCard,
    prevSourceCard,
    isMultiStaff,
    layout,
    highlight,
    showServiceInfo = true,
    showDrivingTimeInfo = false,
    drivingTimeDisplayInfo,
    isDriveFromStart = false,
    onPress,
    onLongPress: onLongPressCallback,
    onDrag,
    onDragEnd,
    opacity,
    showLoading,
    rescheduleProps: rescheduleProps,
    cardRef,
    hourTextRef,
  }) => {
    const [touchX, setTouchX] = useState(0);
    const [touchY, setTouchY] = useState(0);
    const [touchLocationX, setTouchLocationX] = useState(0);
    const [touchLocationY, setTouchLocationY] = useState(0);
    const [timerId, setTimerId] = useState<number | null>(null);

    const { isEnableToNewFlow } = useInvoiceReinvent();
    const [panResponder, setPanResponder] = useState<PanResponderInstance>();
    const [business, calendarConfiguration, permission, preAuthMap, staffMap] = useListSelector(
      selectBusiness(),
      selectCalendarSettings(),
      selectCurrentPermissions(),
      preAuthDetailMapBox,
      staffMapBox,
    );

    const { cardMaximumWidth, agendaCellPaddingLeft, agendaCellPaddingRight, appointmentsViewWidth } = layout;
    const customerInfo = sourceCard.getCustomerInfo();

    const {
      renderDrivingTimeBetweenApptsIndicator,
      renderDrivingTimeToEndLocationIndicator,
      drivingTimeHeight,
      drivingOutHeight,
    } = useDrivingTimeForCardView(
      showDrivingTimeInfo,
      sourceCard,
      cellHeight,
      prevSourceCard,
      drivingTimeDisplayInfo,
      isDriveFromStart,
    );

    const getHoursString = (): string => {
      if (rescheduleProps != null) {
        let startTime = rescheduleProps.startTime;
        let endTime = rescheduleProps.startTime.clone().add(rescheduleProps.duration, 'minute');
        if (sourceCard.checkIfIsBlockFromTickets()) {
          const { start, end } = getBlockXDayStartAndEnd(startTime, startTime, endTime);
          [startTime, endTime] = [start, end];
        }
        return `${rescheduleProps.startTime.format(business.getTimeFormat())} - ${endTime.format(
          business.getTimeFormat(),
        )}`;
      }

      const startTimeInMillionSeconds = sourceCard.startTimeDiffFromStartOfDayInMilliSeconds();
      const endTimeInMillionSeconds = sourceCard.endTimeDiffFromStartOfDayInMilliSeconds();

      if (startTimeInMillionSeconds == null || endTimeInMillionSeconds == null) {
        return '';
      }
      return `${business.formatFixedTime(startTimeInMillionSeconds)} - ${business.formatFixedTime(
        endTimeInMillionSeconds,
      )}`;
    };

    const renderNormalText = (text: string, style: StyleProp<TextStyle> = { marginBottom: 4 } as any) => {
      if (!text || text.trim().length === 0) {
        return null;
      }

      return (
        <MoeText style={[TS_12, style]} numberOfLines={numberOfLines}>
          {text}
        </MoeText>
      );
    };

    const onPanResponderMove = useLatestCallback((event, gestureState) => {
      if (timerId) {
        clearTimeout(timerId);
        setTimerId(null);
      }
      const touchCurrentXtoCardRight = sourceCard.left + sourceCard.width - event.nativeEvent.locationX;
      const touchCurrentXtoCardLeft = event.nativeEvent.locationX - sourceCard.left;
      const moveCardBasedX =
        touchCurrentXtoCardLeft < 0
          ? -Math.abs(touchCurrentXtoCardLeft)
          : touchCurrentXtoCardRight < 0
            ? Math.abs(touchCurrentXtoCardRight)
            : 0;
      if (onDrag) {
        onDrag({
          touchCurrentX: gestureState.moveX,
          touchCurrentY: gestureState.moveY,
          touchBaseX: touchX,
          touchBaseY: touchY,
          touchBaseLocationX: touchLocationX,
          touchBaseLocationY: touchLocationY,
          moveCardBasedX: moveCardBasedX,
          cardLeft: sourceCard.left,
          cardTop: sourceCard.top,
          touchLocationX: event.nativeEvent.locationX,
          touchLocationY: event.nativeEvent.locationY,
        });
      }
    });

    const onLongPressPanResponder = () => {
      return PanResponder.create({
        //TODO(mercer): ATTENTION HERE!
        onStartShouldSetPanResponder: (evt, gestureState) => true,
        onMoveShouldSetPanResponder: (evt, gestureState) => true,
        onMoveShouldSetPanResponderCapture: () => true,

        onPanResponderGrant: (event, gestureState) => {
          // 获取触摸点的基准位置
          setTouchX(event.nativeEvent.pageX);
          setTouchY(event.nativeEvent.pageY);
          setTouchLocationX(event.nativeEvent.locationX);
          setTouchLocationY(event.nativeEvent.locationY);
        },
        onPanResponderMove: onPanResponderMove,
        onPanResponderTerminate: (event, gestureState) => {
          setPanResponder(undefined); // Clear panResponder when user release on long press
          if (onDragEnd != null) {
            onDragEnd();
          }
        },
        onPanResponderTerminationRequest: () => {
          return false;
        },
        onPanResponderRelease: (event, gestureState) => {
          setPanResponder(undefined); // Clear panResponder when user release on long press
          if (onDragEnd != null) {
            onDragEnd();
          }
        },
      });
    };

    const onLongPress = () => {
      if (disableDragging) {
        return;
      }

      Haptics.impactAsync();
      let reasons: string[] = [];
      let reasonsInALine: string = "This appointment couldn't be dragged due to the following reason(s):";
      const withBDServiceType = judgeTicketWithBDService(sourceCard.ticketList);
      if (sourceCard.getAppointmentStatus() === CardStatus.Paid) {
        reasons.push('The appointment has been paid.');
      }
      if (sourceCard.getAppointmentStatus() === CardStatus.Unpaid) {
        reasons.push("The appointment hasn't been paid.");
      }
      if (sourceCard.isOnlineBooking()) {
        reasons.push('The appointment is booked via online booking.');
      }
      if (withBDServiceType) {
        reasons.push('Please process in the web version for now since it contains boarding or daycare services.');
      }
      reasons.forEach((val, index) => {
        reasonsInALine = reasonsInALine + '\n';
        reasonsInALine = reasonsInALine + (index + 1).toString() + '.' + val;
      });
      if (reasons.length > 0) {
        gotIt(reasonsInALine);
        return;
      } else {
        setTouchX(0);
        setTouchY(0);
        setTouchLocationX(0);
        setTouchLocationY(0);
        setPanResponder(onLongPressPanResponder());
        if (onLongPressCallback) {
          onLongPressCallback();
        }
      }
    };

    const renderCardOfNormalMode = () => {
      const containStatusWidth = sourceCard.width < 200;
      const halfWidth = sourceCard.width < cardMaximumWidth / 2;
      const backgroundColor = getHeaderBackgroundColor();
      const txtColor = getContrastTxtColor(backgroundColor, COL_WHITE);
      const headerStyle = [
        styles.header,
        {
          backgroundColor,
        },
      ];
      const statusStyle = [
        TS_12,
        {
          color: getCardHeaderRightText().color,
        },
        containStatusWidth && {
          maxWidth: 60,
        },
      ] as any[];

      // for appointment with short service time
      if (sourceCard?.height <= HeaderHeight) {
        return (
          <TouchableOpacity
            style={getAppointmentCalculatedLayout()}
            key={sourceCard.ticketId == null ? '' : sourceCard.ticketId}
            onPress={onPress}
            onLongPress={onLongPress}
            ref={cardRef}
          >
            {renderDriveInTimeIndicator()}
            {renderDrivingTimeBetweenApptsIndicator()}
            {showLoading ? (
              <ActivityIndicator
                size={'small'}
                color={COL_GRAY}
                style={{ position: 'absolute', bottom: 5, right: 5, zIndex: 20 }}
              />
            ) : null}
            <View style={[...headerStyle, { borderRadius: 6 }]}>
              <View style={{ width: '50%' }}>{renderSingleLine(txtColor)}</View>
              <Full />
              {sourceCard.width < cardMaximumWidth * (2 / 3) ? null : (
                <>
                  <View style={[MR_4]}>{getCardHeaderRightText()?.icon}</View>
                  <MoeText style={statusStyle} numberOfLines={1}>
                    {getCardHeaderRightText().string}
                  </MoeText>
                </>
              )}
            </View>
            {renderDriveOutTimeIndicator()}
            {renderDrivingTimeToEndLocationIndicator()}
          </TouchableOpacity>
        );
      }
      const cardHeight = sourceCard.height - HeaderHeight;
      const bodyStyle = [
        {
          display: 'flex',
          displayDirection: 'column',
          // cardHeight might be NAN
          height: Number.isFinite(cardHeight) ? cardHeight : 30,
          backgroundColor: getBodyBackgroundColor(),
          borderBottomLeftRadius: 6,
          borderBottomRightRadius: 6,
          paddingRight: sourceCard.width < cardMaximumWidth / 4 ? 0 : 8,
          paddingLeft: sourceCard.width < cardMaximumWidth / 4 ? 0 : 8,
          paddingVertical: 4,
          overflow: 'hidden',
        },
      ] as StyleProp<any>[];
      const circleFlag = sourceCard.width - 9 < 13;
      const statusCircleStyle = {
        width: circleFlag ? sourceCard.width - 2 : 13,
        height: circleFlag ? sourceCard.width - 2 : 13,
        borderRadius: circleFlag ? (sourceCard.width - 2) / 2 : 13 / 2,
        backgroundColor: getCardHeaderRightText().color,
      };

      const preAuthInfo = preAuthMap.mustGetItem(sourceCard.ticketId);
      const preAuthHighlight =
        !sourceCard.isPaymentStatusVisible({ isInvoiceNewFlow: isEnableToNewFlow }) &&
        !sourceCard.isOnlineBooking() &&
        preAuthInfo.isToBeCapture;
      const highlightStyle: ViewStyle = preAuthHighlight ? PRE_AUTH_HL_STYLE : {};
      return (
        <TouchableOpacity
          style={{
            ...getAppointmentCalculatedLayout(),
            ...highlightStyle,
          }}
          key={sourceCard.ticketId == null ? '' : sourceCard.ticketId}
          onPress={onPress}
          onLongPress={onLongPress}
          onPressOut={() => {
            const id = setTimeout(() => {
              setPanResponder(undefined);
              if (onDragEnd != null) {
                onDragEnd();
              }
            }, 300) as any;
            setTimerId(id);
          }}
          ref={cardRef}
        >
          {renderDriveInTimeIndicator()}
          {renderDrivingTimeBetweenApptsIndicator()}
          {showLoading ? (
            <ActivityIndicator
              size={'small'}
              color={COL_GRAY}
              style={{ position: 'absolute', bottom: 5, right: 5, zIndex: 20 }}
            />
          ) : null}
          <View style={headerStyle}>
            <View style={[styles.row, { flex: 1 }]}>
              {rescheduleProps != null ? (
                <MoeInput
                  ref={hourTextRef}
                  containerStyle={{
                    justifyContent: 'flex-start',
                    paddingTop: 0,
                    paddingLeft: 0,
                    paddingRight: 0,
                    flex: 1,
                  }}
                  contentStyle={{
                    justifyContent: 'flex-start',
                    backgroundColor: undefined,
                    paddingLeft: 0,
                    paddingRight: 0,
                    minHeight: 20,
                    borderWidth: 0,
                    borderRadius: 0,
                    flex: 1,
                  }}
                  style={[styles.hourText, { color: txtColor }]}
                  value={getHoursString()}
                  editable={false}
                  pointerEvents={'none'}
                  onLongPress={onLongPress}
                />
              ) : (
                <MoeText style={[styles.hourText, { color: txtColor }]} numberOfLines={2}>
                  {getHoursString()}
                </MoeText>
              )}
              {sourceCard.isRepeated() && (
                <View style={{ paddingHorizontal: 1 }}>
                  <IconIcRepeatGreenSvg width={16} height={16} />
                </View>
              )}
              {sourceCard.getAlertNotes()?.length > 0 && (
                <TouchableOpacity
                  style={{ paddingHorizontal: 1 }}
                  onPress={async () => {
                    await gotIt(sourceCard.getAlertNotes(), 'OK');
                  }}
                  onLongPress={onLongPress}
                >
                  <IconIcWarningSvg width={14} height={14} />
                </TouchableOpacity>
              )}
              {(!sourceCard.getCustomerId() || !sourceCard.getCustomerCoordinate()) &&
                business.appType !== BusinessType.GroomingSalon && <IconIcWarningTriangleSvg />}
            </View>
            {isMultiStaff || halfWidth ? (
              <View style={statusCircleStyle} />
            ) : (
              <>
                <View style={[MR_4]}>{getCardHeaderRightText()?.icon}</View>
                <MoeText style={statusStyle}>{getCardHeaderRightText().string}</MoeText>
              </>
            )}
          </View>
          {showServiceInfo ? (
            <View style={bodyStyle}>
              {renderCustomerFullName()}
              {renderServices()}
              {renderTotalPrice()}
              {renderAddress()}
              {renderComment()}
              {renderPhone()}
            </View>
          ) : (
            <View style={bodyStyle} />
          )}
          {renderDriveOutTimeIndicator()}
          {renderDrivingTimeToEndLocationIndicator()}
        </TouchableOpacity>
      );
    };

    // TODO(mercer): smart scheduling drive in here
    const getDriveInInfo = (): [number, string] | undefined => {
      let indicatorText = '';
      let height = DriveInIndicatorViewMinHeight;

      if (sourceCard.driveInTime != null) {
        // 由驾驶时间来控制高度，展示前后 slot 的 ”衔接“ UI。
        height = Math.max((cellHeight / 60) * sourceCard.driveInTime, height);
        indicatorText += `${sourceCard.driveInTime} min${sourceCard.driveInTime > 1 ? 's' : ''}`;
      }
      if (sourceCard.driveInMiles != null) {
        const distanceText = business.formatDistanceText(sourceCard.driveInMiles);
        indicatorText += `${indicatorText ? ' - ' : ''}${distanceText}`;
      }
      if (indicatorText !== '' && sourceCard.isDriveFromStart) {
        indicatorText += ' from start location';
      }
      if (indicatorText !== '') {
        return [height, indicatorText];
      }
      if (sourceCard.width < cardMaximumWidth) {
        return;
      }
      return;
    };

    // TODO(mercer): smart scheduling drive in here
    const renderDriveInTimeIndicator = () => {
      const info = getDriveInInfo();
      if (!info) {
        return;
      }
      const [height, indicatorText] = info;
      return (
        <View
          style={{
            width: '100%',
            height,
            backgroundColor: 'transparent',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <IconIcCarOrange2 />
            <MoeText style={[TS_16_ORANGE, ML_4]}>{indicatorText}</MoeText>
          </View>
          <View style={{ flex: 1, backgroundColor: 'transparent' }}>
            <Dash
              style={{ width: 1, height: '100%', flexDirection: 'column' }}
              dashLength={5}
              dashGap={5}
              dashThickness={2}
              dashColor={COL_ORANGE}
            />
          </View>
        </View>
      );
    };

    // TODO(mercer): smart scheduling drive out here
    const getDrivingOutInfo = (): [number, string] | undefined => {
      let indicatorText = '';
      let height = DriveInIndicatorViewMinHeight;

      if (sourceCard.driveOutTime != null) {
        // 由驾驶时间来控制高度，展示前后 slot 的 ”衔接“ UI。
        height = Math.max((cellHeight / 60) * sourceCard.driveOutTime, height);
        indicatorText += `${sourceCard.driveOutTime} min${sourceCard.driveOutTime > 1 ? 's' : ''}`;
      }
      if (sourceCard.driveOutMiles != null) {
        const distanceText = business.formatDistanceText(sourceCard.driveOutMiles);
        indicatorText += `${indicatorText ? ' - ' : ''}${distanceText}`;
      }
      if (indicatorText !== '' && sourceCard.isDriveToEnd) {
        indicatorText += ' to end location';
      }
      if (indicatorText !== '') {
        return [height, indicatorText];
      }
      if (sourceCard.width < cardMaximumWidth) {
        return;
      }
      return;
    };

    // TODO(mercer): smart scheduling drive out here
    const renderDriveOutTimeIndicator = () => {
      const info = getDrivingOutInfo();
      if (!info) {
        return;
      }
      const [height, indicatorText] = info;
      return (
        <View
          style={{
            width: '100%',
            height,
            backgroundColor: 'transparent',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <View style={{ flex: 1, backgroundColor: 'transparent' }}>
            <Dash
              style={{ width: 1, height: '100%', flexDirection: 'column' }}
              dashLength={5}
              dashGap={5}
              dashThickness={2}
              dashColor={COL_ORANGE}
            />
          </View>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <IconIcCarOrange2 />
            <MoeText style={[TS_16_ORANGE, ML_4]}>{indicatorText}</MoeText>
          </View>
        </View>
      );
    };

    // 获取背景色 staffColor / ApptColor / serviceColor
    const getHeaderBackgroundColor = () => {
      if (highlight) {
        return COL_ORANGE;
      }

      if (sourceCard.isOnlineBooking()) {
        return hexToRgba(COL_ORANGE, 0.5);
      }

      const mainTicket = sourceCard.getFirstTicket();
      switch (calendarConfiguration?.showColorCodeWith) {
        case ShowColorCodeWithType.StaffColor:
          const staffColorCode = staffMap.mustGetItem(mainTicket?.staffId).colorCode;
          return staffColorCode && staffColorCode.trim() ? staffColorCode : COL_BLACK;
        case ShowColorCodeWithType.AppointmentColor:
          return sourceCard.getColorCodeFromFirstTicket();
        case ShowColorCodeWithType.ServiceColor:
          return sourceCard.getColorCodeFromFirstService();
        default:
          return COL_BLACK;
      }
    };

    const getBodyBackgroundColor = () => {
      if (sourceCard.isOnlineBooking()) {
        return hexToRgba(COL_WHITE, 0.5);
      }

      return COL_WHITE;
    };

    const getCardHeaderRightText = (): { string: string; color: string; icon?: ReactNode } => {
      const icon = sourceCard.isShowAutoAssign() ? <IconAutoAssignWhiteSvg width={16} height={16} /> : undefined;
      if (highlight || sourceCard.modifying) {
        return { string: 'SELECTED', color: COL_WHITE, icon };
      }

      if (sourceCard.isOnlineBooking()) {
        return { string: 'BOOK ONLINE', color: COL_WHITE, icon };
      }

      if (sourceCard.checkIfIsBlockFromTickets()) {
        return { string: 'BLOCKED', color: COL_BLACK, icon };
      }

      let status = sourceCard.getAppointmentStatusWithoutPaid();
      return {
        string: status ? CardStatus.mapLabels[status].toUpperCase() : '',
        color: status ? groomingCardStatusColorMap[status] : COL_WHITE,
        icon,
      };
    };

    const renderServices = (style?: StyleProp<TextStyle>) => {
      if (sourceCard.getCustomerId() == null) {
        return undefined;
      }

      return sourceCard.getPets().map((pet, index) => (
        <React.Fragment key={pet.name || index}>
          {calendarConfiguration?.showPetName ? <PetRow pet={pet} /> : null}
          {calendarConfiguration?.showServiceName
            ? renderNormalText(pet.petDetailList.map((petDetail) => petDetail.serviceName).join(', '), [
                { marginBottom: 4 },
                style,
              ] as StyleProp<TextStyle>)
            : null}
        </React.Fragment>
      ));
    };

    const calcPaddingTop = () => {
      const startHour = Math.floor(calendarConfiguration.calendarViewStartAt / 60);
      const startMinute = calendarConfiguration.calendarViewEndAt % 60;
      const calendarStartDiff = sourceCard.startTime
        .hour(startHour)
        .minute(startMinute)
        .diff(sourceCard.startTime, 'minute');
      if (calendarStartDiff > 0) {
        return (CellHeight * calendarStartDiff) / 60;
      }
      return undefined;
    };

    const getAppointmentCalculatedLayout = () => {
      // For rescheduling card:
      if (rescheduleProps) {
        let height = rescheduleProps.height < HeaderHeight ? HeaderHeight : rescheduleProps.height;
        let top = rescheduleProps.top;
        const drivingInInfo = getDriveInInfo();
        const drivingOutInfo = getDrivingOutInfo();
        if (drivingInInfo || drivingOutInfo) {
          height += (drivingInInfo?.[0] || 0) + (drivingOutInfo?.[0] || 0);
          top -= drivingInInfo?.[0] || 0;
        }
        if (drivingTimeHeight) {
          height += drivingTimeHeight;
          top -= drivingTimeHeight;
        }
        if (drivingOutHeight) {
          height += drivingOutHeight;
        }
        const style = StyleSheet.create({
          container: {
            borderColor: getHeaderBackgroundColor(),
            borderRadius: 6,
            borderStyle: 'dashed',
            borderWidth: 1,
            display: 'flex',
            flexDirection: 'column',
            height,
            left: rescheduleProps.left,
            overflow: 'hidden',
            position: 'absolute',
            top,
            width: rescheduleProps.width,
            zIndex: 1,
          },
        });
        return style.container;
      }
      let height = sourceCard.height < HeaderHeight ? HeaderHeight : sourceCard.height;
      let top = sourceCard.top;
      const drivingInInfo = getDriveInInfo();
      const drivingOutInfo = getDrivingOutInfo();
      if (drivingInInfo || drivingOutInfo) {
        height += (drivingInInfo?.[0] || 0) + (drivingOutInfo?.[0] || 0);
        top -= drivingInInfo?.[0] || 0;
      }
      if (drivingTimeHeight) {
        height += drivingTimeHeight;
        top -= drivingTimeHeight;
      }
      if (drivingOutHeight) {
        height += drivingOutHeight;
      }
      const borderStyle: any = sourceCard.modifying
        ? {
            borderWidth: 1,
            borderStyle: 'dashed',
            borderColor: getHeaderBackgroundColor(),
          }
        : {};
      const style = StyleSheet.create({
        container: {
          borderRadius: 6,
          display: 'flex',
          flexDirection: 'column',
          height,
          left: sourceCard.left + agendaCellPaddingLeft,
          overflow: 'hidden',
          paddingTop: calcPaddingTop(),
          position: 'absolute',
          top,
          width: sourceCard.width,
          zIndex: drivingInInfo || drivingOutInfo ? 2 : 1,
          ...borderStyle,
        },
      });
      return style.container;
    };

    const renderCustomerFullName = (small?: boolean) => {
      let customerNameTxtMaxLenWidth = '100%';
      if (sourceCard.isRepeated() && sourceCard.getAlertNotes()?.length > 0) {
        customerNameTxtMaxLenWidth = '65%';
      } else if (sourceCard.isRepeated() && sourceCard.getAlertNotes()?.length === 0) {
        customerNameTxtMaxLenWidth = '80%';
      } else if (!sourceCard.isRepeated() && sourceCard.getAlertNotes()?.length > 0) {
        customerNameTxtMaxLenWidth = '80%';
      }
      const maxWidth = appointmentsViewWidth - agendaCellPaddingLeft - agendaCellPaddingRight;
      const isTooNarrow = sourceCard.width <= maxWidth / 2;
      return (
        <React.Fragment>
          <View style={[styles.row, { marginBottom: small ? undefined : 2, justifyContent: 'space-between' }]}>
            {calendarConfiguration?.showClientName && calendarConfiguration?.showClientName > 0 ? (
              <View
                style={[
                  styles.row,
                  {
                    flex: 1,
                  },
                ]}
              >
                <MoeText
                  style={[
                    VS_SHRINK,
                    small ? TS_12 : TS_14_DARK,
                    {
                      color: customerInfo?.colorCode || COL_BLACK,
                      maxWidth: customerNameTxtMaxLenWidth as DimensionValue,
                    },
                  ]}
                  numberOfLines={numberOfLines}
                >
                  {sourceCard.getCustomerFullName()}
                </MoeText>
                {sourceCard.isNewCustomer() ? (
                  <MoeTag theme="orange" label={'New'} containerStyle={[H_20, ML_6, PH_6]} labelStyle={TS_10_WHITE} />
                ) : null}
              </View>
            ) : null}
            {isTooNarrow ? null : renderCustomerInfoRight()}
          </View>
          {isTooNarrow && (
            <View style={[styles.row, { marginBottom: small ? undefined : 2, justifyContent: 'space-between' }]}>
              {renderCustomerInfoRight()}
            </View>
          )}
        </React.Fragment>
      );
    };

    const renderCustomerInfoRight = () => {
      const paymentStatusVisible = sourceCard.isPaymentStatusVisible({ isInvoiceNewFlow: isEnableToNewFlow });
      if (paymentStatusVisible) {
        const color = sourceCard.getPaymentStatusProps()?.color || 'transparent';
        return (
          <View style={[styles.paymentStatus, { backgroundColor: color }]}>
            <View style={[styles.paymentStatusCircle]} />
            <MoeText style={[TS_14_WHITE, ML_2]}>{sourceCard.getPaymentStatusText({ inShort: true })}</MoeText>
          </View>
        );
      }

      if (!paymentStatusVisible && !sourceCard.isOnlineBooking()) {
        return <PreAuthStatusIcon ticketId={sourceCard.ticketId} />;
      }

      return null;
    };

    const renderTotalPrice = () => {
      return calendarConfiguration?.showServiceName
        ? renderNormalText(business.formatAmount(sourceCard.getTotalPrice()))
        : null;
    };

    const renderAddress = () => {
      return calendarConfiguration?.showAddress ? (
        <>
          {renderNormalText(customerInfo?.fullAddress || '')}
          {renderNormalText(customerInfo?.city || '')}
          {renderNormalText(customerInfo?.zipcode || '')}
        </>
      ) : null;
    };

    const renderComment = () => {
      return calendarConfiguration.showComment ? renderNormalText(sourceCard.getTicketComment()) : null;
    };

    const renderPhone = () => {
      return permission.has('viewClientPhone') ? (
        <>{renderNormalText(business.formatPhoneNumber(customerInfo?.phoneNumber))}</>
      ) : null;
    };

    const renderSingleLine = (txtColor: string) => {
      if (calendarConfiguration?.showClientName) {
        return <MoeText style={[TS_12_WHITE, { color: txtColor }]}>{sourceCard.getCustomerFullName()}</MoeText>;
      }
      if (calendarConfiguration?.showPetName) {
        return (
          <MoeText style={[TS_12_WHITE, { color: txtColor }]}>
            {sourceCard.getPets()?.[0]?.name} {sourceCard.getPets()?.[0]?.breed}
          </MoeText>
        );
      }
      if (calendarConfiguration?.showServiceName) {
        return renderServices({ color: txtColor, marginBottom: undefined } as any);
      }
      if (calendarConfiguration?.showAddress) {
        return renderNormalText(customerInfo?.fullAddress || '', [{ color: txtColor }] as any);
      }
      if (calendarConfiguration?.showComment) {
        return renderNormalText(sourceCard.getTicketComment(), [{ color: txtColor }] as any);
      }
      return null;
    };

    const renderCardOfUltraSlimMode = () => {
      const circleFlag = sourceCard.width - 9 < 13;
      const headerStyle = [
        styles.header,
        {
          backgroundColor: getHeaderBackgroundColor(),
          paddingLeft: circleFlag ? 0 : 9,
          paddingRight: circleFlag ? 0 : 6,
          justifyContent: circleFlag ? 'center' : 'flex-start',
        },
      ] as StyleProp<ViewProps>;
      const statusCircleStyle = {
        width: circleFlag ? sourceCard.width - 2 : 13,
        height: circleFlag ? sourceCard.width - 2 : 13,
        borderRadius: circleFlag ? (sourceCard.width - 2) / 2 : 13 / 2,
        backgroundColor: getCardHeaderRightText().color,
      };
      // for appointment with short service time
      if (sourceCard?.height <= HeaderHeight) {
        return (
          <TouchableOpacity
            style={getAppointmentCalculatedLayout()}
            key={sourceCard.ticketId == null ? '' : sourceCard.ticketId.toString()}
            onPress={onPress}
            onLongPress={onLongPress}
            ref={cardRef}
          >
            {showLoading ? (
              <ActivityIndicator
                size={'small'}
                color={COL_GRAY}
                style={{ position: 'absolute', bottom: 5, right: 5, zIndex: 20 }}
              />
            ) : null}
            <View style={[headerStyle, { borderRadius: 6 }]}>
              <View style={[statusCircleStyle]} />
            </View>
          </TouchableOpacity>
        );
      }

      const bodyStyle = [
        {
          flex: 1,
          backgroundColor: getBodyBackgroundColor(),
          borderBottomLeftRadius: 6,
          borderBottomRightRadius: 6,
          borderWidth: 0,
          borderColor: getBodyBackgroundColor(),
          paddingHorizontal: 2,
          paddingVertical: 2,
          overflow: 'hidden',
        },
      ] as StyleProp<any>[];
      return (
        <TouchableOpacity
          style={getAppointmentCalculatedLayout()}
          key={sourceCard.ticketId == null ? '' : sourceCard.ticketId.toString()}
          onPress={onPress}
          onLongPress={onLongPress}
          ref={cardRef}
        >
          {showLoading ? (
            <ActivityIndicator
              size={'small'}
              color={COL_GRAY}
              style={{ position: 'absolute', bottom: 5, right: 5, zIndex: 20 }}
            />
          ) : null}
          <View style={headerStyle}>
            <View style={statusCircleStyle} />
            {sourceCard.isRepeated() && (
              <View style={{ paddingHorizontal: 5 }}>
                <IconIcRepeatGreenSvg width={20} height={20} />
              </View>
            )}
          </View>
          {showServiceInfo ? (
            <View style={bodyStyle}>
              {sourceCard.getAlertNotes()?.length > 0 && (
                <TouchableOpacity
                  style={{ paddingHorizontal: 3 }}
                  onPress={() => {
                    gotIt(sourceCard.getAlertNotes(), 'OK');
                  }}
                  onLongPress={onLongPress}
                >
                  <IconIcWarningSvg width={14} height={14} />
                </TouchableOpacity>
              )}
              {renderCustomerFullName(true)}
              {renderServices()}
              {renderTotalPrice()}
              {renderAddress()}
              {renderComment()}
              {renderPhone()}
            </View>
          ) : (
            <View style={bodyStyle} />
          )}
        </TouchableOpacity>
      );
    };

    const renderBlock = () => {
      const halfWidth = sourceCard.width <= cardMaximumWidth / 2;
      const bgColor = sourceCard.getColorCodeFromFirstTicket();
      const txtColor = getContrastTxtColor(bgColor);

      const headerStyle = [styles.header, { backgroundColor: bgColor }];
      const statusStyle = [
        TS_14,
        {
          color: txtColor,
        },
      ] as any[];

      // for appointment with short service time
      if (sourceCard?.height <= HeaderHeight) {
        return (
          <TouchableOpacity
            style={getAppointmentCalculatedLayout()}
            key={sourceCard.ticketId == null ? '' : sourceCard.ticketId.toString()}
            onPress={onPress}
            onLongPress={onLongPress}
            ref={cardRef}
          >
            {showLoading ? (
              <ActivityIndicator
                size={'small'}
                color={COL_GRAY}
                style={{ position: 'absolute', bottom: 5, right: 5, zIndex: 20 }}
              />
            ) : null}
            <View style={[...headerStyle, { borderRadius: 6 }]}>
              {halfWidth ? null : rescheduleProps != null ? (
                <MoeInput
                  ref={hourTextRef}
                  containerStyle={{
                    justifyContent: 'flex-start',
                    paddingTop: 0,
                    paddingLeft: 0,
                    paddingRight: 0,
                    flex: 1,
                  }}
                  contentStyle={{
                    justifyContent: 'flex-start',
                    backgroundColor: undefined,
                    paddingLeft: 0,
                    paddingRight: 0,
                    minHeight: 20,
                    borderWidth: 0,
                    borderRadius: 0,
                    flex: 1,
                  }}
                  style={[styles.hourText]}
                  value={getHoursString()}
                  editable={false}
                  pointerEvents={'none'}
                  onLongPress={onLongPress}
                />
              ) : (
                <MoeText style={[styles.hourText, { color: txtColor }]} numberOfLines={2}>
                  {getHoursString()}
                </MoeText>
              )}
              <MoeText style={statusStyle} numberOfLines={1}>
                {/* 这里暂时没搞懂要不要加 auto assign icon... */}
                {getCardHeaderRightText().string}
              </MoeText>
            </View>
          </TouchableOpacity>
        );
      }

      const bodyStyle = [
        {
          flex: 1,
          backgroundColor: COL_E0,
          borderBottomLeftRadius: 6,
          borderBottomRightRadius: 6,
          paddingRight: sourceCard.width < cardMaximumWidth / 4 ? 0 : 8,
          paddingLeft: sourceCard.width < cardMaximumWidth / 4 ? 0 : 8,
          paddingVertical: 4,
          overflow: 'hidden',
        },
      ] as StyleProp<any>[];
      return (
        <TouchableOpacity
          style={getAppointmentCalculatedLayout()}
          key={sourceCard.ticketId == null ? '' : sourceCard.ticketId.toString()}
          onPress={onPress}
          onLongPress={onLongPress}
          ref={cardRef}
        >
          {showLoading ? (
            <ActivityIndicator
              size={'small'}
              color={COL_GRAY}
              style={{ position: 'absolute', bottom: 5, right: 5, zIndex: 20 }}
            />
          ) : null}
          <View style={headerStyle}>
            {halfWidth ? null : rescheduleProps != null ? (
              <MoeInput
                ref={hourTextRef}
                containerStyle={{
                  justifyContent: 'flex-start',
                  paddingTop: 0,
                  paddingLeft: 0,
                  paddingRight: 0,
                  flex: 1,
                }}
                contentStyle={{
                  justifyContent: 'flex-start',
                  backgroundColor: undefined,
                  paddingLeft: 0,
                  paddingRight: 0,
                  minHeight: 20,
                  borderWidth: 0,
                  borderRadius: 0,
                  flex: 1,
                }}
                style={[styles.hourText]}
                value={getHoursString()}
                editable={false}
                pointerEvents={'none'}
                onLongPress={onLongPress}
              />
            ) : (
              <MoeText style={[styles.hourText, { color: txtColor }]} numberOfLines={2}>
                {getHoursString()}
              </MoeText>
            )}
            {sourceCard.isRepeated() ? (
              <View style={{ paddingHorizontal: 1 }}>
                <IconIcRepeatGreenSvg width={18} height={18} />
              </View>
            ) : null}
            <MoeText style={statusStyle} numberOfLines={1}>
              {getCardHeaderRightText().string}
            </MoeText>
          </View>
          <View style={bodyStyle}>
            <MoeText style={TS_14_DARK}>{sourceCard.getTicketComment()}</MoeText>
          </View>
        </TouchableOpacity>
      );
    };

    let panHandlers = {};
    if (panResponder != null) {
      panHandlers = panResponder.panHandlers as PanResponderInstance;
    }
    if (sourceCard.width == null) {
      return null;
    }

    let card: undefined | JSX.Element;
    if (sourceCard.checkIfIsBlockFromTickets()) {
      card = renderBlock();
    } else {
      card = sourceCard.width <= layout.cardMaximumWidth / 4 ? renderCardOfUltraSlimMode() : renderCardOfNormalMode();
    }

    return (
      <Animated.View {...panHandlers} style={{ opacity: opacity }}>
        {card}
      </Animated.View>
    );
  },
);
