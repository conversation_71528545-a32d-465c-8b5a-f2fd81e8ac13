/*
 * @since 2021-08-09 00:22:10
 * <AUTHOR> <<EMAIL>>
 */

import { RoutePath } from '../../utils/RoutePath';

export interface AddOrEditClientTagParams {
  tagId?: number;
}

export const PATH_ADD_OR_EDIT_CLIENT_TAG = new RoutePath<AddOrEditClientTagParams>('/business/add/or/edit/client/tag');

export interface AddOrEditReferralSourceParams {
  id?: number;
}

export const PATH_ADD_OR_EDIT_REFERRAL_SOURCE = new RoutePath<AddOrEditReferralSourceParams>(
  '/business/add/or/edit/referral/source',
);

export interface AddOrEditTaxParams {
  taxId?: number;
}

export const PATH_ADD_OR_EDIT_TAX = new RoutePath<AddOrEditTaxParams>('/business/add/or/edit/tax');

export interface BusinessPreferenceParams {}

export const PATH_BUSINESS_PREFERENCE = new RoutePath<BusinessPreferenceParams>('/business/preference');

export interface BusinessProfileParams {}

export const PATH_BUSINESS_PROFILE = new RoutePath<BusinessProfileParams>('/business/profile');

export interface BusinessSettingParams {}

export const PATH_BUSINESS_SETTING = new RoutePath<BusinessSettingParams>('/business/setting');

export interface ClientTagsParams {}

export const PATH_CLIENT_TAGS = new RoutePath<ClientTagsParams>('/business/client/tags');

export interface ClosedDateEditParams {
  id?: number;
}

export const PATH_CLOSED_DATE_EDIT = new RoutePath<ClosedDateEditParams>('/business/closed/date/edit');

export interface ClosedDateListParams {}

export const PATH_CLOSED_DATE_LIST = new RoutePath<ClosedDateListParams>('/business/closed/date/list');

export interface HolidaySettingParams {}

export const PATH_HOLIDAY_SETTING = new RoutePath<HolidaySettingParams>('/business/holiday/setting');

export interface ReferralSourceParams {}

export const PATH_REFERRAL_SOURCE = new RoutePath<ReferralSourceParams>('/business/referral/source');

export interface TaxListParams {}

export const PATH_TAX_LIST = new RoutePath<TaxListParams>('/business/tax/list');

export interface DefaultSettingParams {}

export const PATH_DEFAULT_SETTING = new RoutePath<DefaultSettingParams>('/business/default/setting');

export interface EditTwilioReplyParams {}

export const PATH_EDIT_TWILIO_REPLY = new RoutePath<EditTwilioReplyParams>('/business/edit/twilio/reply');

export interface MoegoPhoneNumberSettingParams {}

export const PATH_MOEGO_PHONE_NUMBER_SETTING = new RoutePath<MoegoPhoneNumberSettingParams>(
  '/business/moego/phone/number/setting',
);

export interface WorkingHourParams {}

export const PATH_WORKING_HOUR = new RoutePath<WorkingHourParams>('/business/working/hour');
