import { useEffect, useState } from 'react';
import { Platform, ScrollView, useWindowDimensions } from 'react-native';
import { Condition } from '../../../components/Condition';
import { MoeModal, MoeModalHeader } from '../../../components/MoeModal';
import { MoeText } from '../../../components/MoeText';
import { SafeBottomBox } from '../../../components/SafeView';
import { SaveButton } from '../../../components/buttons/SaveButton';
import { InputWithError } from '../../../components/inputs/InputWithError';
import { selectBusiness } from '../../../modules/Business/store/business.selectors';
import { useAsyncCallback } from '../../../utils/hooks/useAsyncCallback';
import { useBool } from '../../../utils/hooks/useBool';
import { useKeyboardHeight } from '../../../utils/hooks/useKeyboardHeight';
import { useThunkDispatch } from '../../../utils/store/action';
import { useListSelector } from '../../../utils/store/selector';
import { COL_ORANGE } from '../../../utils/style/consts';
import { VS_FULL } from '../../../utils/style/misc';
import { MH_20 } from '../../../utils/style/preset/margin';
import { shadowHeavilyStyle } from '../../../utils/style/utils';
import { useMetaData } from '../../Common/store/metadata.hooks';
import { CustomizedForPawParazzi } from '../../Common/store/metadata.type';
import { InvoiceRefundModel, RefundModal } from '../../Payment/store/payment.boxes';
import { RefundCheckBoxList } from '../components/RefundCheckBoxList';
import { RefundSelectorList } from '../components/RefundSelectorList';
import { refundSubmit } from '../store/actions/private/payment.actions';
import { styles } from './Refund.styles';

export interface RefundModalProps {
  onClose: () => void;
  onOk?: () => Promise<void>;
  refundDetails: RefundModal;
  showCheckBoxList?: boolean;
  invoiceRefundParams?: InvoiceRefundModel;
  invoiceId: number;
}

interface RefundDetail {
  refundAmount: number;
  channelList: Record<string, RefundItem>;
}

export interface RefundItem {
  value: string;
  name: string;
  amount: number;
}

export const Refund = (props: RefundModalProps) => {
  const { onClose, onOk, invoiceRefundParams: { refundDetails, callback } = {}, invoiceId } = props;
  const dispatch = useThunkDispatch();

  const [processedData, setProcessedData] = useState<RefundDetail>();
  const [reason, setReason] = useState('');
  const [selectedRefund, setSelectedRefund] = useState<string[]>([]);
  const [business] = useListSelector(selectBusiness());
  const isFinished = useBool(!refundDetails?.isCombination);
  const keyboardHeight = useKeyboardHeight();
  const { height } = useWindowDimensions();
  const [data] = useMetaData<CustomizedForPawParazzi>('customized_for_PawParazzi');
  const reasonError = useBool();

  useEffect(() => {
    const { channelList, refundAmount, isCombination } = refundDetails || {};
    const mapList = {} as Record<string, any>;

    channelList?.forEach((item) => {
      const { paymentMethod, paymentId, canRefundAmount } = item || {};
      mapList[paymentId] = {
        name: paymentMethod,
        amount: canRefundAmount,
        value: paymentId,
      };
    });

    if (!isCombination && channelList?.length) {
      const [firstOne] = Object.keys(mapList);
      setSelectedRefund([firstOne]);
    }

    setProcessedData({
      refundAmount: refundAmount as number,
      channelList: mapList,
    });

    return () => {
      setProcessedData({} as RefundDetail);
    };
  }, [refundDetails]);

  const handleSubmit = useAsyncCallback(async () => {
    if (data?.requiredRefundReason && !reason) {
      reasonError.open();
      return;
    }
    if (!selectedRefund?.length || !processedData?.channelList) {
      return;
    }

    const refunds = selectedRefund.reduce(
      (pre: { canRefundAmount: number; paymentId: number; paymentMethod: string }[], cur: string) => {
        const { amount: canRefundAmount, name: paymentMethod } = processedData?.channelList[cur];
        pre.push({
          canRefundAmount,
          paymentId: +cur,
          paymentMethod,
        });
        return pre;
      },
      [],
    );

    const params = {
      invoiceId: +invoiceId,
      refundAmount: refundDetails?.refundAmount,
      refundReason: reason,
      refunds,
    };

    await dispatch(refundSubmit(params));

    if (callback) {
      await callback();
    }
    await onOk?.();
  });

  const handleClose = () => {
    onClose?.();
  };

  const handleChangeText = (text: string) => {
    const trimmedText = text.trim();
    setReason(trimmedText);
    if (data?.requiredRefundReason) {
      reasonError.as(trimmedText.length === 0);
    }
  };

  const handleSelectChange = (value: string[]) => {
    setSelectedRefund(value);
  };

  if (!processedData?.channelList) {
    return null;
  }

  return (
    <MoeModal x={0} y={0} bottom mask={true} style={[styles.modal]} visible={true} onClose={handleClose}>
      <MoeModalHeader title={'Issue refund'} onClose={handleClose} />
      <ScrollView style={{ maxHeight: Platform.OS === 'ios' ? height * 0.75 : (height - keyboardHeight) * 0.75 }}>
        <MoeText style={[styles.headerText, styles.headerTextWrap]}>
          <MoeText style={[styles.headerText, styles.amountText]}>
            {`${refundDetails?.refundAmount && business.formatAmount(refundDetails.refundAmount)} `}
          </MoeText>
          has been deducted from the total invoiced amount, a refund to the original payment method will be issued.
          Please confirm to proceed.
        </MoeText>
        <MoeText style={styles.selectTitle}>Refund to</MoeText>

        <Condition if={!!refundDetails?.isCombination}>
          <RefundCheckBoxList
            isOk={isFinished}
            dataList={processedData.channelList}
            refundAmount={processedData.refundAmount}
            selectedRefund={selectedRefund}
            onSelectChange={handleSelectChange}
          />
        </Condition>
        <Condition if={!refundDetails?.isCombination}>
          <RefundSelectorList
            isOk={isFinished}
            dataList={processedData.channelList}
            refundAmount={processedData.refundAmount}
            selectedRefund={selectedRefund}
            onSelectChange={handleSelectChange}
          />
        </Condition>

        <InputWithError
          multiline
          placeholder="Refund reason for this ticket"
          onChangeText={handleChangeText}
          contentStyle={styles.commentInput}
          maxLength={300}
          returnKeyType="done"
          error={reasonError.value}
          errorText="Refund reason is required."
          label={
            <MoeText style={styles.inputTextTitleWrap}>
              Reason for refund
              {data?.requiredRefundReason ? (
                <MoeText style={[{ color: COL_ORANGE }]}> *</MoeText>
              ) : (
                <MoeText style={styles.optional}> &nbsp;(Optional)</MoeText>
              )}
            </MoeText>
          }
        />

        <SafeBottomBox>
          <SaveButton
            loading={handleSubmit.loading}
            disabled={!isFinished.value || reasonError.value}
            text={'Issue refund'}
            style={[
              VS_FULL,
              MH_20,
              shadowHeavilyStyle(),
              {
                height: 56,
                borderRadius: 28,
              },
            ]}
            onPress={handleSubmit}
          />
        </SafeBottomBox>
      </ScrollView>
    </MoeModal>
  );
};
