import {
  FNK_CustomerModel,
  OrderChargeType,
  OrderType,
  RealmType,
  useChargeOrder,
} from '@moego/finance-business-app-kit';
import { ID_ANONYMOUS } from '@moego/finance-utils';
import { useFocusEffect } from '@react-navigation/native';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { RefreshControl, ScrollView, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Condition } from '../../../components/Condition';
import { SafeBottom } from '../../../components/SafeView';
import { Switch } from '../../../components/SwitchCase';
import { FinanceKit } from '../../../services/finance-kit';
import { useHeaderColor, useHeaderTitle } from '../../../utils/hooks/useEditMenu';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSafeBottom } from '../../../utils/hooks/useSafeArea';
import { StackNavigation } from '../../../utils/navigation';
import { useListSelector } from '../../../utils/store/selector';
import { COL_NEUTRAL_FILLED } from '../../../utils/style/consts';
import { VS_CONTAINER, VS_FULL, VS_ROW } from '../../../utils/style/misc';
import { BG_NEUTRAL_FILLED } from '../../../utils/style/preset/backgroundColor';
import { MH_20, MT_20 } from '../../../utils/style/preset/margin';
import { PB_40, PH_20 } from '../../../utils/style/preset/padding';
import { toast, useToastCallback } from '../../../utils/toast';
import { getAppointment } from '../../Appointment/store/appt.api';
import { customerMapBox } from '../../Customer/store/customer.boxes';
import { getGroomingTicket } from '../../Grooming/store/actions/public/grooming_ticket.actions';
import { selectGroomingTicketInvoice } from '../../Grooming/store/grooming_invoice.selectors';
import { OrderParams, PreCheckOutParams } from '../Payment.api';
import { useOrderDetailItem } from '../hooks/useOrderDetailItem';
import { selectBusinessPaymentSetting } from '../store/payment.selectors';
import { OrderContext } from './OrderContext';
import { useRedirectToInvoiceList } from './hooks/useRedirectToInvoiceList';
import { useSyncOrderToStore } from './hooks/useSyncOrderToStore';
import { AddMoreServiceCard } from './modules/AddMoreServices/AddMoreServiceCard';
import { CustomerCard } from './modules/Customer/CustomerCard';
import { ExtraChargeCard } from './modules/ExtraCharge/ExtraChargeCard';
import { OrderActions } from './modules/OrderActions/OrderActions';
import { RefundActions } from './modules/OrderActions/RefundButton';
import { useTakePaymentActionsControl } from './modules/OrderActions/useTakePaymentActionsControl';
import { PaymentSummaryCard } from './modules/PaymentSummary/PaymentSummaryCard';
import { ProductInfoCard } from './modules/ProductInfo/ProductInfoCard';
import { ServiceInfoCard } from './modules/ServiceInfo/ServiceInfoCard';

export interface IOrderProps {
  params: PreCheckOutParams & OrderParams;
  navigation: StackNavigation;
}

// 现在还没有对用户 roll out order 的概念, 但是内部我们先统一叫法
// 后续会慢慢向用户 roll out order 的概念
export const Order = memo<IOrderProps>(({ params, navigation }) => {
  const { invoiceId: orderId, originOrderId } = params;
  const [chargeType, setChargeType] = useState(params.chargeType ?? OrderChargeType.Payment);
  const computedOrderId = useMemo(
    () =>
      chargeType === OrderChargeType.ExtraCharge || chargeType === OrderChargeType.ExtraTips ? originOrderId! : orderId,
    [chargeType, originOrderId, orderId],
  );
  const legacyInvoice = useSelector(selectGroomingTicketInvoice(computedOrderId));
  const dispatch = useDispatch();
  const [customer, paymentSetting] = useListSelector(
    customerMapBox.mustGetItem(legacyInvoice.customerId),
    selectBusinessPaymentSetting(),
  );

  const { isLoading: originOrderLoading, order: originOrder } = useOrderDetailItem(originOrderId ?? ID_ANONYMOUS);
  useHeaderTitle(`Confirm Invoice #${orderId}`);
  useHeaderColor(COL_NEUTRAL_FILLED);
  const orderContext = useChargeOrder(FinanceKit, {
    chargeType,
    entityId: orderId,
    customer: FinanceKit.buildModel(RealmType.Customer, customer as FNK_CustomerModel),
    paymentSetting: FinanceKit.buildModel(RealmType.PaymentSetting, paymentSetting),
  });

  // fix charegeType when navigate to order page
  useEffect(() => {
    if (orderContext.order.state?.orderType === OrderType.EXTRA) {
      setChargeType(OrderChargeType.ExtraCharge);
    } else if (orderContext.order.state?.orderType === OrderType.EXTRA_TIPS) {
      setChargeType(OrderChargeType.ExtraTips);
    }
  }, [orderContext.order.state]);

  const refreshAppointment = useToastCallback(async () => {
    if (!orderContext.order.state?.groomingId) return;
    toast.loading();
    await Promise.all([
      dispatch(getAppointment({ appointmentId: String(orderContext.order.state.groomingId) })),
      dispatch(getGroomingTicket(orderContext.order.state.groomingId)),
    ]);
  });

  const refreshOrder = useToastCallback(async () => {
    toast.loading();
    return await orderContext.order.refresh();
  });

  const context = useMemo(() => {
    return {
      ...orderContext,
      chargeType,
      order: orderContext.order.state!,
      orderError: orderContext.order.error,
      orderUpdating: orderContext.order.updating,
      refreshOrder,
      refreshAppointment,
      orderId,
      isLoading: orderContext.loading,
      customer: customer,
      originOrderId,
      originOrder,
      customerId: customer.customerId ?? 0,
      isExtraCharge: chargeType === OrderChargeType.ExtraCharge || chargeType === OrderChargeType.ExtraTips,
      module: 'grooming',
    };
  }, [orderContext, chargeType, refreshOrder, refreshAppointment, orderId, customer, originOrderId, originOrder]);

  useSyncOrderToStore({
    order: orderContext.order.state,
    chargeType,
  });
  useRedirectToInvoiceList({ order: orderContext.order.state });

  useFocusEffect(
    useLatestCallback(() => {
      refreshOrder();
    }),
  );

  const isLoading = orderContext.loading || originOrderLoading;
  const orderActionsControl = useTakePaymentActionsControl({
    orderId,
    chargeType,
  });
  const safeBottomHeight = useSafeBottom(37);
  const remainAmountV2 = context.order?.remainAmountV2 ?? 0;
  const needRefund = Boolean(remainAmountV2 < 0);
  return (
    <OrderContext.Provider value={context}>
      <ScrollView
        refreshControl={<RefreshControl refreshing={orderContext.loading} onRefresh={orderContext.order.refresh} />}
        style={[VS_CONTAINER, BG_NEUTRAL_FILLED]}
        contentContainerStyle={[PH_20, PB_40]}
      >
        {!isLoading && (
          <>
            <CustomerCard customerId={customer.customerId} />
            <ExtraChargeCard />
            {chargeType !== OrderChargeType.ExtraTips ? (
              <>
                <ServiceInfoCard style={[MT_20]} />
                <AddMoreServiceCard />
                <ProductInfoCard order={context.order} />
              </>
            ) : null}
            <PaymentSummaryCard />
            <Switch shortCircuit>
              <Switch.Case if={needRefund}>
                <RefundActions />
              </Switch.Case>
              <Switch.Case if={!orderActionsControl.showCompleteInvoiceOnly}>
                <OrderActions />
              </Switch.Case>
            </Switch>
          </>
        )}
        <SafeBottom />
      </ScrollView>
      <Condition if={!isLoading && !needRefund && orderActionsControl.showCompleteInvoiceOnly}>
        <View style={[VS_ROW, VS_FULL, MH_20, { position: 'absolute', bottom: safeBottomHeight }]}>
          <OrderActions />
        </View>
      </Condition>
    </OrderContext.Provider>
  );
});
