import { OrderPaymentStatusEnum, OrderType } from '@moego/finance-business-app-kit';
import { CreateTokenResult, HandleNextActionResult, Token } from '@stripe/stripe-react-native';
import { splice } from 'monofile-utilities/lib/array';
import { ANY, T_SECOND } from 'monofile-utilities/lib/consts';
import { sleep } from 'monofile-utilities/lib/sleep';
import { F0 } from 'monofile-utilities/lib/types';
import { startCheckoutAsync } from 'react-native-square-reader-sdk';
import { OpenApiModels } from '../../../../types/openApi/schema';
import { Sentry } from '../../../../utils/NativeModules';
import { EnumValues } from '../../../../utils/createEnum';
import { StackNavigation } from '../../../../utils/navigation';
import { MoeStore } from '../../../../utils/store/createMoeStore';
import { omitEmpty } from '../../../../utils/store/utils';
import { toast } from '../../../../utils/toast';
import { capture, currencyToSmallestDenomination, toFixed } from '../../../../utils/utils';
import { BusinessRecord } from '../../../Business/store/business.boxes';
import { selectBusiness } from '../../../Business/store/business.selectors';
import { PATH_GROOMING_TICKET_DETAIL } from '../../../Grooming/Grooming.api';
import {
  getGroomingTicketInvoice,
  placeGroomingTicketOrder,
} from '../../../Grooming/store/actions/public/grooming_invoice.actions';
import { getGroomingTicket } from '../../../Grooming/store/actions/public/grooming_ticket.actions';
import { GroomingTicketInvoiceRecord, PaymentStatusEnum } from '../../../Grooming/store/grooming_invoice.boxes';
import { selectGroomingTicketInvoice } from '../../../Grooming/store/grooming_invoice.selectors';
import { currentStaffIdBox } from '../../../Staff/store/staff.boxes';
import { selectStaff } from '../../../Staff/store/staff.selectors';
import { PATH_CHARGED_SUCCESS, PATH_PRE_CHECK_OUT } from '../../Payment.api';
import {
  CreatePaymentParams,
  captureStripeTerminalPaymentIntent,
  confirmGeneralPayment,
  createAndConfirmStripePayment,
  createGeneralPayment,
  createStripeTerminalPaymentIntent,
  deleteStripePaymentMethod,
  getSquareReaderPaymentStatus,
  getStripePaymentMethod,
  submitSquarePayment,
  submitSquareReaderPayment,
  submitSquareTerminalPayment,
} from '../../store/actions/private/payment.actions';
import { addSquareCard, removeSquareCard } from '../../store/actions/public/square.actions';
import {
  PaymentStatus,
  PrefabPaymentMethod,
  SquarePaymentMethod,
  StripePaymentMethod,
  paymentMethodMapBox,
} from '../../store/payment.boxes';
import { InvoiceModuleType, InvoiceStatus } from '../../store/payment_invoice.selectors';
import { squareCustomerMapBox } from '../../store/square.boxes';
import { StripePaymentIntentStatus, stripePaymentMethodMapBox } from '../../store/stripe.boxes';
import { closeSaveCardModal, openSaveCardModal } from '../SaveCardModal/SaveCardModal.boxes';
import {
  GeneralPayInput,
  IsDeposit,
  MoePayStatus,
  StripeCardInfo,
  StripePaymentInput,
  TakePaymentInput,
  defaultInput,
  generateSquarePaymentNote,
} from './MoePay.options';

// 本地拉取后台的支付状态
enum LocalPaymentState {
  UNKNOWN,
  SUCCESS,
  FAILED,
}

interface StripeApi {
  handleNextAction: (paymentIntentClientSecret: string) => Promise<HandleNextActionResult>;
  createToken: (params: Token.CreateParams) => Promise<CreateTokenResult>;
}

// notes: 在此计算各接口请求参数(store get)，便于清晰了解流程，具体接口请求在 store 中，便于可能需要的数据更新(store set)
export class MoePay {
  private store: MoeStore = ANY;
  private invoiceId: number = 0;
  private moePaymentId: number = 0;
  private listeners: F0[] = [];
  private input: TakePaymentInput = { ...defaultInput };
  private status: MoePayStatus = 'initializing';
  private module: InvoiceModuleType = 'grooming';
  private navigation: StackNavigation = ANY;
  private business: BusinessRecord = ANY;
  private customerId: number = ANY;
  private groomingId: number = ANY;
  private staffId: number = ANY;
  private earlyInvoice: GroomingTicketInvoiceRecord = ANY; // attention: without instance methods.
  private stripePaymentIntentId: string = ANY;

  private setStatus(status: MoePayStatus) {
    this.status = status;
    this.listeners.forEach((fn) => fn());
  }

  getStatus() {
    return this.status;
  }

  subscribe(fn: F0) {
    this.listeners.push(fn);
    return () => {
      splice(this.listeners, fn);
    };
  }

  config(invoiceId: number, store: MoeStore, module: InvoiceModuleType, navigation: StackNavigation) {
    this.invoiceId = invoiceId;
    this.store = store;
    this.module = module;
    this.navigation = navigation;
  }

  /**
   * 设置 completePayment 用到的 moePaymentId
   * 使用场景：
   * 1. 已有 moePaymentId 的情况下，手动设置（在 SquarePos 的 PaymentPosCheck 页面用到）
   * 2. 发起支付时，读取后台返回的 moePaymentId 并设置
   * @param id MoeGo 后台的 paymentId
   */
  setMoePaymentId(id: number) {
    this.moePaymentId = id;
  }

  private initialPayment(input: Partial<TakePaymentInput>) {
    const invoice = this.store.select(selectGroomingTicketInvoice(this.invoiceId));
    this.business = this.store.select(selectBusiness());
    this.staffId = this.store.select(selectStaff()).id;
    this.groomingId = invoice.groomingId;
    this.customerId = invoice.customerId;
    this.input = {
      ...this.input,
      ...omitEmpty(input),
      tipsAmount: input.tipsAmount || 0,
      isDeposit: input.isDeposit || 0,
    };
    this.earlyInvoice = { ...invoice } as GroomingTicketInvoiceRecord;
    this.setStatus('submitting');
  }

  async startPay(vendor: string, subType: string, input: Partial<TakePaymentInput>) {}

  async startGeneralPay(methodId: number, input: Partial<GeneralPayInput>) {
    this.initialPayment(input);

    try {
      const { store, invoiceId, earlyInvoice } = this;
      if (earlyInvoice.status === InvoiceStatus.Created) {
        await store.dispatch(placeGroomingTicketOrder(invoiceId));
      }

      const { isDeposit } = this.input;
      const createParams = this.getGeneralPayParams(methodId);
      const payment = await store.dispatch(createGeneralPayment(createParams));
      this.setMoePaymentId(payment.id);
      await store.dispatch(confirmGeneralPayment(invoiceId));
      await this.completePayment(true, isDeposit);
    } catch (e) {
      this.sendLog(e as Error);
      throw e;
    } finally {
      this.setStatus('idle');
    }
  }

  private getGeneralPayParams(methodId: number): CreatePaymentParams {
    const { store, invoiceId, module, customerId } = this;
    const { amount, paidBy, description, checkNumber, isDeposit } = this.input;
    const methodMap = this.store.select(paymentMethodMapBox);
    const createParams: CreatePaymentParams = {
      customerId,
      invoiceId,
      paidBy,
      description,
      amount: toFixed(amount),
      checkNumber,
      isDeposit,
      method: methodMap.mustGetItem(methodId).name || PrefabPaymentMethod.mapLabels[methodId],
      methodId,
      module: module,
      staffId: store.select(currentStaffIdBox),
      cardNumber: ANY,
      cardType: ANY,
      expMonth: ANY,
      expYear: ANY,
      isOnline: false,
      signature: ANY,
      stripePaymentMethodId: ANY,
    };

    return createParams;
  }

  async startSquarePosPay(input: Partial<TakePaymentInput>) {
    this.initialPayment(input);

    try {
      const { earlyInvoice, store } = this;
      if (earlyInvoice.status === InvoiceStatus.Created) {
        await store.dispatch(placeGroomingTicketOrder(this.invoiceId));
      }

      const reqParams: OpenApiModels['POST/payment/square/reader/payments']['Req'] = await this.getSquarePayParams();
      const initPayRes = await store.dispatch(submitSquareReaderPayment(reqParams));
      const primaryId = initPayRes.data?.primaryId;
      if (!primaryId) {
        throw new Error(initPayRes.message || 'Payment failed');
      }

      this.setMoePaymentId(primaryId);
      return {
        primaryId,
      };
    } catch (e) {
      this.sendLog(e as Error);
      throw e;
    } finally {
      this.setStatus('idle');
    }
  }

  async startSquarePosOtherPay(methodId: number, input: Partial<GeneralPayInput>) {
    this.initialPayment(input);
    try {
      const { store, invoiceId, earlyInvoice, navigation } = this;
      if (earlyInvoice.status === InvoiceStatus.Created) {
        await store.dispatch(placeGroomingTicketOrder(invoiceId));
      }

      const createParams: CreatePaymentParams = this.getGeneralPayParams(methodId);
      const payment = await store.dispatch(createGeneralPayment(createParams));
      this.setMoePaymentId(payment.id);
      await store.dispatch(confirmGeneralPayment(invoiceId));
      await store.dispatch(getGroomingTicketInvoice(this.invoiceId));
      this.updateStateAfterFinished(false);
      navigation.dispatch(PATH_GROOMING_TICKET_DETAIL.navigate({ ticketId: earlyInvoice.groomingId }));
      navigation.dispatch(PATH_CHARGED_SUCCESS.push({ invoiceId: invoiceId }));
    } catch (e) {
      this.sendLog(e as Error);
      throw e;
    }
  }

  async startStripePay(input: StripePaymentInput, stripeApi: StripeApi, autoToast = true) {
    this.initialPayment(input);

    try {
      const { store, invoiceId, customerId } = this;

      // place order
      if (this.earlyInvoice.status === InvoiceStatus.Created) {
        await store.dispatch(placeGroomingTicketOrder(invoiceId, { autoToast }));
      }

      // create charge token or get COF card info
      const cardInfo: StripeCardInfo = await this.getStripeCardInfo(stripeApi);
      // take payment
      const { description, saveNewCard, signature, paidBy, isDeposit } = this.input;
      const reqParams: OpenApiModels['POST/payment/payment/createAndConfirm']['Req'] = {
        customerId,
        invoiceId,
        description,
        amount: toFixed(input.amount + input.tipsAmount),
        tipsAmount: toFixed(input.tipsAmount),
        paidBy,
        saveCard: saveNewCard,
        methodId: PrefabPaymentMethod.CreditCard,
        module: this.module,
        staffId: this.staffId,
        signature,
        isDeposit,
        addProcessingFee: input.addProcessingFee,
        ...cardInfo,
      };

      const r = await store.dispatch(createAndConfirmStripePayment(reqParams, { autoToast }));
      if (r.data.stripeStatus === StripePaymentIntentStatus.RequiresAction) {
        const { error } = await stripeApi.handleNextAction(r.data.stripeClientSecret);
        if (error) {
          throw error;
        }
      }
      this.setMoePaymentId(r.data.id);

      // 5. mark complete and routing
      await this.completePayment(true, isDeposit, { autoToast });
    } catch (e) {
      this.sendLog(e as Error);
      throw e;
    } finally {
      this.setStatus('idle');
    }
  }

  async createStripeTerminalPaymentIntent(
    {
      readerId,
      locationId,
      stripePaymentMethod,
    }: {
      readerId: string;
      locationId: string;
      stripePaymentMethod: EnumValues<typeof StripePaymentMethod>;
    },
    input: Partial<StripePaymentInput>,
  ) {
    this.initialPayment(input);
    // 获取支付相关参数以及 paymentId 提供 terminal 方使用
    const params = {
      ...this.getGeneralPayParams(PrefabPaymentMethod.CreditCard),
      tipsAmount: toFixed(input.tipsAmount ?? 0),
      readerId,
      locationId,
      stripePaymentMethod,
      addProcessingFee: input.addProcessingFee,
    };
    const paymentIntent = await this.store.dispatch(createStripeTerminalPaymentIntent(params));
    this.setMoePaymentId(paymentIntent.primaryId);
    this.stripePaymentIntentId = paymentIntent.paymentIntentId;
    return {
      ...params,
      ...paymentIntent,
    };
  }

  async captureStripeTerminalPayment(paymentIntentId: string) {
    await this.store.dispatch(captureStripeTerminalPaymentIntent(paymentIntentId));
  }

  private async getStripeCardInfo(stripeApi: StripeApi) {
    const { paidBy, cardOnFile, cardOnFileId } = this.input;
    if (cardOnFile) {
      const { card: cardInfo } = this.store.select(stripePaymentMethodMapBox).mustGetItem(cardOnFileId);
      return {
        expYear: String(cardInfo?.exp_year),
        expMonth: String(cardInfo?.exp_month),
        cardType: cardInfo?.brand || '',
        cardNumber: cardInfo?.last4 || '',
        stripePaymentMethodId: cardOnFileId,
        stripePaymentMethod: StripePaymentMethod.CardOnFile,
        chargeToken: ANY,
      };
    }

    const result = await stripeApi.createToken({
      type: 'Card',
      name: paidBy,
    });
    if (result.error) {
      throw result.error;
    }

    const { card: cardInfo } = result.token;
    return {
      expYear: String(cardInfo?.expYear || ''),
      expMonth: String(cardInfo?.expMonth || ''),
      cardNumber: cardInfo?.last4 || '',
      cardType: cardInfo?.brand || '',
      stripePaymentMethodId: ANY,
      stripePaymentMethod: StripePaymentMethod.Card,
      chargeToken: result.token.id,
    };
  }

  async startSquareCardNumberOrCofPay(input: Partial<TakePaymentInput>) {
    this.initialPayment(input);

    try {
      const { store, invoiceId, earlyInvoice } = this;
      await this.saveSquareCard();
      if (earlyInvoice.status === InvoiceStatus.Created) {
        await store.dispatch(placeGroomingTicketOrder(invoiceId));
      }

      const reqParams: Omit<OpenApiModels['POST/payment/square/payments']['Req'], 'tipsAmount'> =
        await this.getSquarePayParams();
      const r = await store.dispatch(submitSquarePayment({ ...reqParams, tipsAmount: toFixed(this.input.tipsAmount) }));
      this.setMoePaymentId(r.data.primaryId);
      await this.completePayment(true, input.isDeposit);
    } catch (e) {
      await this.deleteSquareCard();
      this.sendLog(e as Error);
      throw e;
    } finally {
      this.setStatus('idle');
    }
  }

  async startSquareReaderPay(input: Partial<TakePaymentInput>) {
    this.initialPayment(input);

    try {
      const { invoiceId, earlyInvoice, store } = this;
      if (earlyInvoice.status === InvoiceStatus.Created) {
        await store.dispatch(placeGroomingTicketOrder(this.invoiceId));
      }

      const reqParams: OpenApiModels['POST/payment/square/reader/payments']['Req'] = await this.getSquarePayParams();
      const initPayRes = await store.dispatch(submitSquareReaderPayment(reqParams));
      const primaryId = initPayRes.data?.primaryId;
      if (!primaryId) {
        throw new Error(initPayRes.message || 'Payment failed');
      }

      this.setMoePaymentId(primaryId);
      // 4. take square reader payment
      const currencyCode = this.business.currencyCode;
      await startCheckoutAsync({
        tipSettings: input.skipTipping
          ? undefined
          : {
              showCustomTipField: true,
              showSeparateTipScreen: true,
              tipPercentages: [15, 20, 25],
            },
        additionalPaymentTypes: ANY,
        skipReceipt: true,
        collectSignature: true,
        allowSplitTender: false,
        delayCapture: false,

        // MoeGo #INVOICE_ID-primaryId-square_payment_method
        note: generateSquarePaymentNote(invoiceId, primaryId, SquarePaymentMethod.Reader),
        amountMoney: {
          // amount transfer, see https://github.com/square/react-native%2dsquare-reader-sdk/blob/master/docs/reference.md#money
          amount: currencyToSmallestDenomination(reqParams.amount!, currencyCode || ''),
          currencyCode: currencyCode || '',
        },
      });

      return {
        primaryId,
      };
    } catch (e) {
      this.sendLog(e as Error);
      throw e;
    } finally {
      this.setStatus('idle');
    }
  }

  async startSquareTerminalPay(input: Partial<TakePaymentInput>) {
    this.initialPayment(input);

    try {
      const { invoiceId, customerId, module, groomingId, store } = this;
      const { description, deviceId, paidBy, amount, isDeposit, skipTipping } = this.input;
      const reqParams: OpenApiModels['POST/payment/square/terminal/charge']['Req'] = {
        invoiceId,
        customerId,
        description,
        deviceId,
        paidBy,
        module,
        groomingId,
        amount,
        isDeposit,
        isOnline: false,
        cardNonce: '',
        useCOF: false,
        businessId: ANY,
        currency: ANY,
        locationId: ANY,
        signature: ANY,
        squareCustomerId: ANY,
        staffId: ANY,
        squarePaymentId: ANY,
        merchant: ANY,
        skipTipping,
      };
      const data = await store.dispatch(submitSquareTerminalPayment(reqParams));
      this.setMoePaymentId(data.moePaymentId);
      return data;
    } catch (e) {
      this.sendLog(e as Error);
      throw e;
    } finally {
      this.setStatus('idle');
    }
  }

  private async getSquarePayParams() {
    const { store, input, invoiceId, module, customerId, groomingId } = this;
    const useCOF = !!(input.cardOnFileId || input.saveNewCard);
    return {
      deviceId: ANY,
      currency: ANY,
      locationId: ANY,
      businessId: ANY,
      squarePaymentId: ANY,
      merchant: ANY,
      isOnline: false,

      customerId,
      invoiceId,
      groomingId,
      useCOF,
      module,
      cardNonce: useCOF ? input.cardOnFileId : input.cardNonce,
      amount: toFixed(input.amount + input.tipsAmount),
      description: input.description,
      paidBy: input.paidBy,
      signature: input.signature,
      isDeposit: input.isDeposit,
      isSquarePos: input.isSquarePos,
      skipTipping: input.skipTipping,

      squareCustomerId: store.select(squareCustomerMapBox.mustGetItem(customerId)).squareCustomerId || ANY,
      staffId: store.select(currentStaffIdBox),
    };
  }

  private async saveSquareCard() {
    const input = this.input;
    const invoice = this.store.select(selectGroomingTicketInvoice(this.invoiceId));
    if (!input.cardOnFileId && !input.cardNonce) {
      throw new Error('Cannot load square card.');
    }

    if (input.saveNewCard) {
      const info = await this.store.dispatch(
        addSquareCard({
          customerId: invoice.customerId,
          cardHolderName: input.paidBy,
          cardNonce: input.cardNonce,
          billingAddress: ANY,
        }),
      );

      this.input.cardOnFileId = info.id;
    }
  }

  private deleteSquareCard = async () => {
    const { input, store, customerId } = this;
    if (input.saveNewCard && input.cardOnFileId) {
      await store.dispatch(removeSquareCard(customerId, input.cardOnFileId));
    }
  };

  private async confirmSaveCard(autoToast = true) {
    return new Promise<void>(async (resolve, _reject) => {
      const { store } = this;
      const r = await store.dispatch(getStripePaymentMethod(this.stripePaymentIntentId, { autoToast }));
      const { newCard, cardNumber, cardType, paymentMethodId } = r;

      // 只有新卡才需要确认 SaveCard
      if (newCard && paymentMethodId) {
        toast();
        store.dispatch(
          openSaveCardModal({
            mode: 'Show',
            cardNumber: cardNumber,
            cardType: cardType,
            onCancel: async () => {
              store.dispatch(deleteStripePaymentMethod(paymentMethodId));
              store.dispatch(closeSaveCardModal());
              await sleep(200);
              resolve();
            },
            onConfirm: async () => {
              store.dispatch(closeSaveCardModal());
              await sleep(200);
              resolve();
            },
          }),
        );
      } else {
        resolve();
      }
    });
  }

  public async completePayment(
    needFetchInvoice: boolean = true,
    isDeposit: IsDeposit = 0,
    options?: {
      finishType?: 'success' | 'manual';
      saveCard?: boolean;
      autoToast?: boolean;
    },
  ) {
    if (needFetchInvoice) {
      await this.fetchState();
    }

    const { invoiceId, store, navigation } = this;
    const invoice = store.select(selectGroomingTicketInvoice(invoiceId));
    const ticketId = invoice.groomingId;

    this.updateStateAfterFinished(false);

    // 能到这里说明已经支付成功了(除了 SquarePos)
    // TODO(xiaoyang): completePayment 包含的逻辑过多: Square/Square Terminal/Square Pos/Stripe/Stripe Terminal/Others 等等，需要拆分
    if (options?.saveCard) {
      try {
        await this.confirmSaveCard(options.autoToast);
      } catch (error) {
        this.sendLog(error as Error);
      }
    }
    // 支付 deposit
    if (isDeposit === 1) {
      navigation.dispatch(PATH_GROOMING_TICKET_DETAIL.navigate({ ticketId }));
      return;
    }

    // 完全支付，finished
    const updatedInvoice = store.select(selectGroomingTicketInvoice(invoiceId));

    // note: scanQrCode 情况（needFetchInvoice = false），不会走到 else 分支：1. invoice 已刷新（轮询过）；2. invoice 状态是 Completed
    // new invoice 全额支付后的 paymentStatus 可能(跟appt status有关)会等于 PAID，老的 invoice 不会等于这个，因此可以不用判断白名单
    if (
      updatedInvoice.status === InvoiceStatus.Completed ||
      updatedInvoice.paymentStatus === OrderPaymentStatusEnum.PAID
    ) {
      navigation.dispatch(PATH_GROOMING_TICKET_DETAIL.navigate({ ticketId }));
      navigation.dispatch(PATH_CHARGED_SUCCESS.push({ invoiceId: invoiceId }));
    } else {
      // partial pay / fail / status not updated yet.
      const currentPayment = invoice.paymentSummary.payments.find((payment) => payment.id === this.moePaymentId);
      if (currentPayment?.status === PaymentStatusEnum.PAID || currentPayment?.status === PaymentStatusEnum.COMPLETED) {
        toast('success', 'Charge successfully', 2000);
      }

      navigation.dispatch(
        PATH_PRE_CHECK_OUT.navigate({
          invoiceId: invoiceId,
          originOrderId: +(invoice.orderType === OrderType.EXTRA ? invoice.orderRefId : invoiceId),
        }),
      );
    }
  }

  // after finish(success or failed), async update ticket and invoice.
  public async updateStateAfterFinished(needFetchInvoice: boolean = true) {
    const { store, invoiceId } = this;
    const invoice = store.select(selectGroomingTicketInvoice(invoiceId));
    const ticketId = invoice.groomingId;
    store.dispatch(getGroomingTicket(ticketId)).catch(capture);

    // need't to fetch invoice again if alreay call fetchState
    if (needFetchInvoice !== false) {
      store.dispatch(getGroomingTicketInvoice(invoiceId)).catch(capture);
    }
  }

  // private handleCheckoutSuccessButError(e: Error, msg: string = 'Charge successfully') {
  //   toast('success', msg, 2000);
  //   const { groomingId, navigation } = this;
  //   this.sendLog(e, CUSTOM_DEFINE_ERROR.CheckoutSuccessButUpdateError.code);
  //   navigation.dispatch(PATH_GROOMING_TICKET_DETAIL.navigate({ ticketId: groomingId }));
  // }

  /**
   * 轮询拉取支付状态
   * TODO: 完善 UNKNOWN / FAILED 交互体验
   */
  private async fetchState() {
    if (!this.earlyInvoice) {
      this.earlyInvoice = this.store.select(selectGroomingTicketInvoice(this.invoiceId));
    }

    if (!this.moePaymentId) {
      console.warn('MoePay.fetchState: empty moePaymentId');
      return LocalPaymentState.UNKNOWN;
    }

    const earlyRemainAmount = this.earlyInvoice.remainAmount;

    for (let i = 0; i < 5; i++) {
      await this.store.dispatch(getGroomingTicketInvoice(this.invoiceId)).catch(capture);
      const invoice = this.store.select(selectGroomingTicketInvoice(this.invoiceId));
      // The invoice status is not necessarily updated after the payment record is updated, because updating invoice
      // need some time. To check whether the payment record of currently initiated payment is finished:
      // 1. The payment record is in completed status
      // 2. The remainAmount of current invoice is updated
      // i === 5: 目的是不改变原来的逻辑
      const currentPayment = invoice.paymentSummary.payments.find((payment) => payment.id === this.moePaymentId);
      if (
        (invoice.remainAmount !== earlyRemainAmount || i === 4) &&
        (currentPayment?.status === PaymentStatusEnum.PAID || currentPayment?.status === PaymentStatusEnum.COMPLETED)
      ) {
        return LocalPaymentState.SUCCESS;
      }
      // 确定失败，不再轮询，跳出循环
      if (currentPayment?.status === PaymentStatusEnum.FAILED) {
        return LocalPaymentState.FAILED;
      }

      await sleep(T_SECOND);
    }
    return LocalPaymentState.UNKNOWN;
  }

  public async checkReaderPaymentSuccess(primaryId: number, completedRequired: boolean = false) {
    const r = await this.store.dispatch(getSquareReaderPaymentStatus(primaryId)).catch(capture);
    const status = r ? r.status : -1;
    switch (status) {
      case PaymentStatus.Completed:
      case PaymentStatus.Processing:
      case PaymentStatus.Paid:
        return completedRequired ? status === PaymentStatus.Completed : true;
    }

    return false;
  }

  private sendLog(error: Error, name?: string) {
    error.name = 'MobilePaymentError';
    Sentry.captureException(error, {
      fingerprint: [error.name, name].filter(Boolean) as string[],
    });
  }
}
