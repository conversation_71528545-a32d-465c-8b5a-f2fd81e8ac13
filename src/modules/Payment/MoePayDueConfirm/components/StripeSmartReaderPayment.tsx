import { Reader } from '@stripe/stripe-terminal-react-native';
import { sleep } from 'monofile-utilities/lib/sleep';
import React, { memo, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import GifStripeSmartReaderSearch from '../../../../../assets/images/gifs/stripe-smart-reader-search.gif';
import icAddOrange from '../../../../../assets/images/ic-add-orange.svg';
import IconIcSmartReaderPng from '../../../../../assets/images/ic-smart-reader.png';
import { MoeText } from '../../../../components/MoeText';
import { SafeBottom } from '../../../../components/SafeView';
import { BlockButton } from '../../../../components/buttons/BlockButton';
import { SaveButton } from '../../../../components/buttons/SaveButton';
import { TextButton } from '../../../../components/buttons/TextButton';
import { MoeImage } from '../../../../components/images/MoeImage';
import { Sentry } from '../../../../utils/NativeModules';
import { gotIt, primaryConfirmAsync } from '../../../../utils/alert';
import { useAsyncEffect } from '../../../../utils/hooks/react';
import { useAsyncCallback } from '../../../../utils/hooks/useAsyncCallback';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useMoeTerminalCleanup } from '../../../../utils/hooks/useMoeTerminalCleanup';
import { useSetState } from '../../../../utils/hooks/useSetState';
import { useStackNavigation } from '../../../../utils/navigation';
import { useThunkDispatch } from '../../../../utils/store/action';
import { useMoeStore } from '../../../../utils/store/createMoeStore';
import { useListSelector } from '../../../../utils/store/selector';
import { COL_ORANGE } from '../../../../utils/style/consts';
import { VS_CONTAINER } from '../../../../utils/style/misc';
import { MB_12, MH_20, MT_10, MT_20, MT_40, MT_8 } from '../../../../utils/style/preset/margin';
import { PH_20, PT_16 } from '../../../../utils/style/preset/padding';
import { textStyle } from '../../../../utils/style/utils';
import { loading, toast } from '../../../../utils/toast';
import { unwrap } from '../../../../utils/unwrapStripeSdkResult';
import { getApiErrorMessage, runUntil } from '../../../../utils/utils';
import { selectBusiness } from '../../../Business/store/business.selectors';
import {
  selectCustomerByGroomingTicketInvoice,
  selectGroomingTicketInvoice,
} from '../../../Grooming/store/grooming_invoice.selectors';
import { PATH_MOE_PAY_SMART_READER_ADD } from '../../Payment.api';
import { IsDeposit } from '../../components/MoePay/MoePay.options';
import {
  DiscoverStatus,
  ReaderConnectionStatus,
  SmartReaderWithConnectionStatus,
  normalizeStripeTerminalErrorMsg,
  sendStripeTerminalLog,
} from '../../components/MoePay/StripeTerminal.options';
import { useMoePay } from '../../components/MoePay/useMoePay';
import { useMoeStripeTerminal } from '../../components/MoePay/useMoeStripeTerminal';
import {
  closeStripeTerminalGuide,
  showStripeTerminalGuide,
} from '../../components/StripeTerminalChargeGuide/StripeTerminalChargeGuide.boxes';
import { useLastMoegoPayChargeReader } from '../../hooks/useLastMoegoPayChargeReader';
import {
  cancelServerPayment,
  checkServerPaymentStatus,
  checkSmartReaderInProgress,
  processServerPayment,
} from '../../store/actions/private/payment.actions';
import { ServerPaymentStatus, StripePaymentMethod, shouldWithTips } from '../../store/payment.boxes';
import { selectBusinessPaymentSetting } from '../../store/payment.selectors';
import { getStripeSmartReaderList } from '../../store/stripeTerminal.actions';
import {
  CANCEL_PAY_CODE,
  currentStripeTerminalLocationIdBox,
  isStripeSmartReaderServerDrivenAvailableCountry,
  lastMoegoPayChargeReader,
  stripeSmartReaderMapBox,
} from '../../store/stripeTerminal.boxes';
import { selectBusinessStripeSmartReaderList } from '../../store/stripeTerminal.selectors';
import { PaymentAmount, usePaymentAmount } from './PaymentAmount';
import { SmartReaderItem } from './SmartReaderItem';
import { StripeSmartReaderInProgressModal } from './StripeSmartReaderInProgressModal';
import { styles } from './StripeSmartReaderPayment.style';

export interface StripeSmartReaderPaymentProps {
  invoiceId: number;
  isDeposit?: IsDeposit;
}

export interface SmartReaderPaymentState {
  disabledReaderList: SmartReaderWithConnectionStatus[];
  availableReaderList: SmartReaderWithConnectionStatus[];
}

const useServerSmartReaderPayment = () => {
  const dispatch = useThunkDispatch();
  const isCanceled = useRef(false);
  const abortController = useRef<AbortController>();

  const onPay = ({
    paymentIntentId,
    readerId,
    tipEligibleAmount,
  }: {
    paymentIntentId: string;
    readerId: string;
    tipEligibleAmount: number;
  }) => {
    return new Promise(async (resolve, reject) => {
      if (!readerId) return;
      isCanceled.current = false;

      try {
        await dispatch(
          processServerPayment(
            {
              paymentIntentId,
              readerId,
              tipEligibleAmount,
            },
            { autoToast: false },
          ),
        );
      } catch (error) {
        reject({
          message: getApiErrorMessage(error as any),
        });
        return;
      }

      const { status, message } = await runUntil(async () => {
        if (isCanceled.current) {
          return {
            status: runUntil.ProcessingStatus.Failed,
            message: CANCEL_PAY_CODE,
          };
        }

        try {
          abortController.current ??= new AbortController();
          const { status, message } = await dispatch(
            checkServerPaymentStatus(
              {
                payIntentId: paymentIntentId,
                readerId,
              },
              { autoToast: false, signal: abortController.current.signal },
            ),
          );

          if (status === ServerPaymentStatus.Succeeded) {
            loading();
            dispatch(closeStripeTerminalGuide());
            return {
              status: runUntil.ProcessingStatus.Succeeded,
            };
          }

          if (status === ServerPaymentStatus.Failed) {
            return {
              status: runUntil.ProcessingStatus.Failed,
              message,
            };
          }

          if (status === ServerPaymentStatus.Waiting) {
            return {
              status: runUntil.ProcessingStatus.Waiting,
            };
          }

          return {
            status: runUntil.ProcessingStatus.Unknown,
          };
        } catch (error) {
          // from cancel token.
          if ((error as any).message === CANCEL_PAY_CODE) {
            return {
              status: runUntil.ProcessingStatus.Failed,
              message: (error as any).message,
            };
          }
          return {
            status: runUntil.ProcessingStatus.Failed,
            message: getApiErrorMessage(error as any),
          };
        }
      });

      if (status === runUntil.ReturnStatus.Succeeded) {
        resolve(undefined);
      }

      if (status === runUntil.ReturnStatus.Failed) {
        if (message === CANCEL_PAY_CODE) {
          reject({ code: CANCEL_PAY_CODE, message });
        } else {
          reject({ message });
        }
      }

      if (status === runUntil.ReturnStatus.Unknown) {
        reject({ message });
      }
    });
  };

  const onCancel = async (readerId: string) => {
    await dispatch(cancelServerPayment({ readerId }));
    isCanceled.current = true;
    abortController.current?.abort(CANCEL_PAY_CODE);
    abortController.current = undefined;
  };

  return {
    onPay,
    onCancel,
  };
};

const useConnectedReader = (useServerDriven: boolean) => {
  const {
    connectedReader: stripeConnectedReader,
    connectionStatus: stripeConnectionStatus,
    discoveredReaders,
    discoverReaders,
    connectInternetReader: connectStripeReader,
    disconnectReader: disconnectStripeReader,
  } = useMoeStripeTerminal();

  const [connectedReader, setConnectedReader] = useState<SmartReaderWithConnectionStatus>();
  const [connectionStatus, setConnectionStatus] = useState<Reader.ConnectionStatus>(
    useServerDriven ? 'notConnected' : stripeConnectionStatus,
  );
  const [smartReaderList, smartReaderMap] = useListSelector(
    selectBusinessStripeSmartReaderList(),
    stripeSmartReaderMapBox,
  );

  // client-driven 模式下使用：根据 stripeConnectionStatus 调整对外的 status。
  useEffect(() => {
    if (useServerDriven) return;
    // connectedReader 状态刷新会比 stripeConnectionStatus 慢，避免 UI 抖动，所以先认为它是 connecting。
    const status = !connectedReader && stripeConnectionStatus === 'connected' ? 'connecting' : stripeConnectionStatus;
    setConnectionStatus(status);
  }, [stripeConnectionStatus, connectedReader, useServerDriven]);

  // client-driven 模式下使用：
  // 根据 terminal SDK connectedReader 设置对外的 connectedReader。
  // 如果 handle disconnect，则会把 connectedReader 置为 undefined。
  useEffect(() => {
    if (useServerDriven) return;
    const readerId = smartReaderList.find((id) => id === stripeConnectedReader?.id);

    if (readerId) {
      const foundReader = smartReaderMap.mustGetItem(readerId);
      setConnectedReader({
        ...foundReader.toJSON(),
        connectionStatus: connectedReader?.connectionStatus,
      });
    } else {
      setConnectedReader(undefined);
    }
  }, [stripeConnectedReader, smartReaderList, smartReaderMap, useServerDriven]);

  const connectReader = async (reader: SmartReaderWithConnectionStatus) => {
    if (connectionStatus === 'connecting') {
      return;
    }

    if (useServerDriven) {
      setConnectedReader({
        ...reader,
        connectionStatus: ReaderConnectionStatus.connected,
      });
      setConnectionStatus('connected');
    } else {
      const stripeReader = discoveredReaders.find((r) => r.serialNumber === reader.serialNumber);

      if (!stripeReader) {
        // Should not reach!
        Sentry.captureException(
          new Error(`available reader ${reader.serialNumber} is missing in discovered readers list`),
        );
        return;
      }
      // TODO(Perqin, P2): Filter by location is not implemented by the SDK yet
      await connectStripeReader({ reader: stripeReader });
    }
  };

  const disconnectReader = async () => {
    if (useServerDriven) {
      setConnectedReader(undefined);
      setConnectionStatus('notConnected');
    } else {
      await disconnectStripeReader();
      // disconnect 之后必须重新 discover。否则 discoveredReaders 会置为空数组。
      await discoverReaders({
        simulated: SIMULATED,
        discoveryMethod: 'internet',
      });
    }
  };

  return {
    connectedReader,
    connectionStatus,
    connectReader,
    disconnectReader,
  };
};

const useGetReaderList = (useServerDriven: boolean) => {
  const dispatch = useThunkDispatch();
  const [locationId, smartReaderList, smartReaderMap] = useListSelector(
    currentStripeTerminalLocationIdBox,
    selectBusinessStripeSmartReaderList(),
    stripeSmartReaderMapBox,
  );
  const store = useMoeStore();
  const {
    discoverReaders: discoverStripeReaders,
    connectedReader,
    discoveredReaders,
    discoveringStatus,
    cancelDiscovering,
  } = useMoeStripeTerminal();

  const [state, setState] = useSetState<SmartReaderPaymentState>({
    availableReaderList: [],
    disabledReaderList: [],
  });

  const discoverReaders = async () => {
    const [smartReaderList] = store.select(selectBusinessStripeSmartReaderList());
    if (connectedReader?.deviceType === 'wisePosE' || !smartReaderList.length || !locationId) {
      return;
    }
    if (discoveringStatus === DiscoverStatus.discovering) {
      await cancelDiscovering();
      // cancelDiscovering doesn't change discovering status immediately, must wait for a while.
      await sleep(1000);
    }
    await discoverStripeReaders({
      simulated: SIMULATED,
      discoveryMethod: 'internet',
    });
  };

  // 获取 reader list 后，SDK discover readers。
  useAsyncEffect(async () => {
    await dispatch(getStripeSmartReaderList(locationId));
    if (!useServerDriven) await discoverReaders();
  }, []);

  /**
   * 根据 server 的 reader list 和 SDK 的 discover readers 进行匹配筛选出：
   * availableReaderList：可使用的 reader。
   *   在 client driven 模式下则是 discover readers 与 server readers 的交集。
   *   在 server driven 模式下则是所有的 server readers。
   * disabledReaderList：
   *   在 client driven 模式下则是 server readers 中排除了 availableReaderList 的部分。
   *   在 server driven 模式下则是空数组。
   * */
  useEffect(() => {
    let disabledReaderList: SmartReaderWithConnectionStatus[] = [];
    let availableReaderList: SmartReaderWithConnectionStatus[] = [];
    smartReaderList.forEach((id) => {
      const curReader = smartReaderMap.mustGetItem(id);
      if (useServerDriven) {
        availableReaderList.push({
          ...curReader.toJSON(),
          connectionStatus: ReaderConnectionStatus.notConnected,
        });
      } else {
        const curReaderInAvailable = discoveredReaders.find((reader) => reader.serialNumber === curReader.serialNumber);
        if (curReaderInAvailable) {
          availableReaderList.push({
            ...curReader.toJSON(),
            connectionStatus: curReaderInAvailable.connectionStatus,
          });
        } else {
          disabledReaderList.push({
            ...curReader.toJSON(),
          });
        }
      }
    });

    setState({
      disabledReaderList,
      availableReaderList,
    });
  }, [smartReaderList, smartReaderMap, discoveredReaders]);

  return state;
};

const SIMULATED = false;

/**
 * 需要确保 stripeSdk 已经初始化
 *
 * 一些变量含义的澄清：
 * 1. smartReaderList：已配对的 reader，也就是已经注册到当前 business 的 reader，由后台接口返回的列表
 * 2. discoveredReaders：已发现的 reader，也就是通过 Stripe SDK 发现的 reader，注意：SDK 可能会发现当前局域网内的所有 reader
 * 3. connectedReader：Stripe SDK 返回的当前已连接的 reader
 * 4. connectedReaderInPaired：已连接且配对的 reader，即(1) && (3)
 * 5. availableReaderList：已发现的且配对的 reader，即(1) && (2)
 * 6. disabledReaderList：不在发现范围内但配对的 reader，即(1) && !(2)
 *
 * 我们限制了必须配对过的 reader 才允许被连接和使用，所以实际渲染的时候只会使用上述的(4)(5)(6)。
 */
export const StripeSmartReaderPayment = memo<StripeSmartReaderPaymentProps>(({ invoiceId, isDeposit }) => {
  const navigation = useStackNavigation();
  const dispatch = useThunkDispatch();
  const [, setLastMoegoPayChargeReader] = useLastMoegoPayChargeReader();
  const inProgressModalVisible = useBool(false);
  const { discoverReaders, discoveringStatus, retrievePaymentIntent, collectPaymentMethod, confirmPaymentIntent } =
    useMoeStripeTerminal();
  useMoeTerminalCleanup();

  const [invoice, locationId, business, smartReaderList, paymentSetting, customer] = useListSelector(
    selectGroomingTicketInvoice(invoiceId),
    currentStripeTerminalLocationIdBox,
    selectBusiness(),
    selectBusinessStripeSmartReaderList(),
    selectBusinessPaymentSetting(),
    selectCustomerByGroomingTicketInvoice(invoiceId),
  );

  const usServerDriven = isStripeSmartReaderServerDrivenAvailableCountry(business.country);
  const paymentDue = isDeposit ? (!!invoice.depositInfo ? invoice.depositInfo.amount : 0) : invoice.remainAmount;
  const withTips = shouldWithTips(!!isDeposit, invoice, paymentSetting, customer);
  const { setState: setPaymentAmountState, ...paymentAmountState } = usePaymentAmount({
    paymentDue,
    isDeposit,
    invoiceId,
  });
  const { availableReaderList, disabledReaderList } = useGetReaderList(usServerDriven);
  const { connectedReader, connectionStatus, connectReader, disconnectReader } = useConnectedReader(usServerDriven);
  const pay = useMoePay(invoiceId);

  const handleDisconnectReader = useAsyncCallback(async () => {
    await primaryConfirmAsync('The current reader will be disconnected', 'Confirm');
    await disconnectReader();
  });

  const { onCancel, onPay } = useServerSmartReaderPayment();

  // TODO(Sam, P2): All paymentIntendId are the same, maybe we can use the same paymentIntendId.
  const handlePayClientStripeReader = useAsyncCallback(
    async (paymentIntentSecret: string, tipEligibleAmount: number) => {
      const retrieveResult = unwrap(await retrievePaymentIntent(paymentIntentSecret));
      const collectResult = unwrap(
        await collectPaymentMethod({
          paymentIntent: retrieveResult.paymentIntent,
          tipEligibleAmount,
        }),
      );
      unwrap(
        await confirmPaymentIntent({
          paymentIntent: collectResult.paymentIntent,
        }),
      );

      loading();
      dispatch(closeStripeTerminalGuide());
    },
  );

  const checkReaderInProgress = async (readerId: string, isConfirmed?: boolean) => {
    if (isConfirmed) {
      return false;
    }

    const info = await dispatch(checkSmartReaderInProgress({ readerId }));
    return info.inProgress;
  };

  const handleReaderInProgressConfirm = () => {
    inProgressModalVisible.close();
    savePayment({
      isReaderInProgressConfirmed: true,
    });
  };

  const savePayment = useAsyncCallback(
    useLatestCallback(async (options?: { isReaderInProgressConfirmed?: boolean }) => {
      if (!connectedReader) {
        toast('fail', 'Need to connect a reader', 1000);
        return;
      }

      const inProgress = await checkReaderInProgress(connectedReader.id, options?.isReaderInProgressConfirmed);
      if (inProgress) {
        inProgressModalVisible.open();
        return;
      }

      try {
        const totalAmountWithProcessingFee = paymentAmountState.amount + paymentAmountState.processingFee;
        const params = await pay.createStripeTerminalPaymentIntent(
          {
            readerId: connectedReader.id,
            locationId,
            stripePaymentMethod: StripePaymentMethod.SmartReader,
          },
          {
            amount: paymentAmountState.amount,
            tipsAmount: 0,
            description: paymentAmountState.description,
            paidBy: paymentAmountState.cardHolderName,
            addProcessingFee: !!paymentAmountState.processingFeeEnabled,
            isDeposit,
          },
        );

        dispatch(
          showStripeTerminalGuide({
            mode: 'SmartReader',
            amount: totalAmountWithProcessingFee,
            onClose: usServerDriven ? () => onCancel(connectedReader?.id!) : undefined,
          }),
        );
        const { paymentIntentSecret, paymentIntentId } = params;

        // if tipEligibleAmount is equal to 0, it means skip tipping.
        // if tipEligibleAmount is equal to undefined, it means that the tip base amount is the amount at the time the paymentIntent is created.
        // TODO(SAM, gq): need to add more currency transformers when add more currency like JPY (currently using cent).
        const tipEligibleAmount = withTips ? invoice.tipBasedAmount * 100 : 0;

        if (usServerDriven) {
          await onPay({ paymentIntentId, readerId: connectedReader?.id!, tipEligibleAmount });
        } else {
          await handlePayClientStripeReader(paymentIntentSecret, tipEligibleAmount);
        }

        await pay.completePayment(true, isDeposit, { saveCard: true });
        setLastMoegoPayChargeReader(lastMoegoPayChargeReader.SmartReader);
        toast();
      } catch (error: any) {
        dispatch(closeStripeTerminalGuide());
        if (error?.code !== CANCEL_PAY_CODE) {
          gotIt(normalizeStripeTerminalErrorMsg(error));
        }
        sendStripeTerminalLog(error);
      }
    }),
  );

  const renderDiscoveryGuide = () => {
    const shouldTryAgain = discoveringStatus === DiscoverStatus.discoverFinished && !availableReaderList.length;

    return (
      <View style={styles.readerDiscoverGuide}>
        <View style={styles.imageWrap}>
          <MoeImage
            style={[styles.readerDiscoveryGif, { opacity: shouldTryAgain ? 0 : 1 }]}
            source={GifStripeSmartReaderSearch}
          />
          <View style={styles.readerImgWrap}>
            <MoeImage style={styles.readerImg} source={IconIcSmartReaderPng} />
          </View>
        </View>

        {!shouldTryAgain && <MoeText style={styles.readerDiscoverTitle}>Searching</MoeText>}

        {shouldTryAgain ? (
          <View style={styles.tryAgain}>
            <MoeText style={[styles.tryAgainText]}>No results.</MoeText>
            <TextButton
              style={[textStyle(14)]}
              label="Try again"
              theme="green"
              onPress={() => {
                // TODO(Perqin, P2): Filter by location is not implemented by the SDK yet
                discoverReaders({
                  simulated: SIMULATED,
                  discoveryMethod: 'internet',
                });
              }}
            />
          </View>
        ) : (
          <MoeText style={styles.readerDiscoverTip}>
            {usServerDriven
              ? 'Please make sure that the reader is powered on and  connected to the Internet.'
              : 'Please make sure that the reader and your phone are connected to the same Wi-Fi.'}
          </MoeText>
        )}
      </View>
    );
  };

  const renderDisabledReaders = () => {
    if (!disabledReaderList?.length) return null;

    return (
      <>
        <MoeText style={[MH_20, MB_12]}>All Readers</MoeText>
        <View style={[{ alignItems: 'center' }, MH_20]}>
          {disabledReaderList.map((reader) => {
            // These readers are not in discovered reader list, do not handle select event
            return <SmartReaderItem key={reader.id} reader={reader} disabled={true} />;
          })}
        </View>
      </>
    );
  };

  const renderDiscoveredReaders = () => {
    if (!availableReaderList?.length) return null;

    return (
      <>
        <MoeText style={[MH_20, MB_12]}>
          {usServerDriven ? 'Device linked to your location' : 'Device on your Wi-Fi network'}
        </MoeText>
        <View style={[{ alignItems: 'center' }, MH_20]}>
          {availableReaderList.map((reader) => (
            <SmartReaderItem
              key={reader.serialNumber}
              reader={reader}
              disabled={
                connectionStatus === 'connecting' && reader.connectionStatus !== ReaderConnectionStatus.connecting
              }
              onSelect={connectReader}
            />
          ))}
        </View>
      </>
    );
  };

  if (connectionStatus === 'connecting') {
    // TODO(perqin, P2): Should show alert instead of infinite loading
    return <ActivityIndicator style={[MT_40]} />;
  }

  if (connectedReader || !smartReaderList.length) {
    return (
      <>
        <ScrollView style={VS_CONTAINER} contentContainerStyle={[PH_20, PT_16]}>
          <View>
            <PaymentAmount
              {...paymentAmountState}
              setState={setPaymentAmountState}
              disableEditAmount={false}
              invoice={invoice}
              stripePaymentMethod={StripePaymentMethod.SmartReader}
              resetValue={1}
            />
          </View>

          {connectedReader && (
            <View>
              <MoeText style={[MT_20, MB_12]}>
                {usServerDriven ? 'Device linked to your location' : 'Device on your Wi-Fi network'}
              </MoeText>
              <SmartReaderItem reader={connectedReader} />
              <TouchableOpacity onPress={() => handleDisconnectReader()}>
                <MoeText style={styles.readerSwitch}>Change to another reader</MoeText>
              </TouchableOpacity>
            </View>
          )}

          {!smartReaderList.length && (
            <BlockButton
              style={[MT_10]}
              size={48}
              theme={'orangeText'}
              icon={icAddOrange}
              iconStyle={{ width: 20, height: 20 }}
              onPress={() => {
                navigation.dispatch(PATH_MOE_PAY_SMART_READER_ADD.navigate({}));
              }}
              text="Pair a reader"
              textStyle={textStyle(void 0, COL_ORANGE)}
            />
          )}
        </ScrollView>
        {connectedReader && (
          <>
            <SaveButton
              style={[MH_20, MT_8]}
              onPress={() => {
                if (savePayment.loading) {
                  return;
                }

                savePayment();
              }}
              disabled={paymentAmountState.amount === 0}
              text={`Charge ${business.formatAmount(paymentAmountState.amount + paymentAmountState.processingFee)}`}
            />
            <SafeBottom />
          </>
        )}

        <StripeSmartReaderInProgressModal
          visible={inProgressModalVisible.value}
          onClose={inProgressModalVisible.close}
          onConfirm={handleReaderInProgressConfirm}
        />
      </>
    );
  }

  return (
    <ScrollView>
      {renderDiscoveryGuide()}
      {renderDiscoveredReaders()}
      {renderDisabledReaders()}
      <SafeBottom />
    </ScrollView>
  );
});
