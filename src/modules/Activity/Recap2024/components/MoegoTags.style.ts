import { StyleSheet } from 'react-native';
import { COL_ORANGE, COL_WHITE } from '../../../../utils/style/consts';
import { textStyle, viewStyle } from '../../../../utils/style/utils';

export const styles = StyleSheet.create({
  box: viewStyle({
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 40,
    backgroundColor: COL_WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 6,
    borderWidth: 2,
    borderColor: '#C8CBD2',
  }),
  activeBox: viewStyle({
    borderColor: COL_ORANGE,
  }),
  tags: textStyle(16, '#333333', {
    fontWeight: '600',
  }),
});
