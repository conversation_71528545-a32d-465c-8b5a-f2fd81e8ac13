import { growthBook } from '../../../../../../../utils/growthBook/growthBook';
import { GrowthBookFeatureList } from '../../../../../../../utils/growthBook/growthBook.config';
import { ServiceAreaZipcodeSelect } from './ServiceAreaZipcodeSelect';

import { useDispatch } from 'amos';
import classNames from 'classnames';
import React, { memo, useEffect, useRef, useState } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../../../../components/Condition';
import { cn, Modal } from '@moego/ui';
import {
  AddServiceAreaModalStep,
  ServiceAreaInfoColorCodeMode,
  ServiceAreaType,
  defaultState,
} from '../../../../../../../components/ServiceArea/ServiceArea.options';
import { useGetPlaceIdList } from '../../../../../../../components/ServiceArea/hooks/useGetPlaceIdByZipcode';
import { verify } from '../../../../../../../components/ServiceArea/hooks/useSubmit';
import { addServiceArea } from '../../../../../../../store/serviceArea/serviceArea.actions';
import { type LatLng } from '../../../../../../../utils/geo';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { DrawServiceArea } from '../DrawServiceArea/DrawServiceArea';
import { ServiceAreaBasicInfo, type ServiceAreaBasicInfoRef } from '../ServiceAreaBasicInfo';
import { AddServiceAreaModalFooter } from './AddServiceAreaModalFooter';
import { ServiceAreaTypeBar } from './ServiceAreaTypeBar';
import { ServiceAreaZipcodeSearchInput } from './ServiceAreaZipcodeSearchInput';

export interface AddServiceAreaModalProps {
  visible: boolean;
  onClose: () => void;
  businessId: number;
}

export const AddServiceAreaModal = memo((props: AddServiceAreaModalProps) => {
  const { visible, onClose, businessId } = props;
  const dispatch = useDispatch();
  const basicInfoRef = useRef<ServiceAreaBasicInfoRef>(null);

  const [data, setData] = useSetState(defaultState);
  const isPolygonType = data.serviceAreaType === ServiceAreaType.Polygon;
  const [currentStep, setCurrentStep] = useState(AddServiceAreaModalStep.DrawOrSelectZipcode);
  const placeIdList = useGetPlaceIdList(data.zipcodes);
  const enableGeoFencing = growthBook.isOn(GrowthBookFeatureList.EnableGeoFencing);

  const handleSave = useLatestCallback(async () => {
    const basicInfo = await basicInfoRef.current?.getData();
    if (basicInfo) {
      const result = await dispatch(
        addServiceArea(
          {
            ...basicInfo,
            ...data,
          },
          businessId,
        ),
      );
      result.success && onClose();
    }
  });
  useEffect(() => {
    if (!visible) {
      setCurrentStep(AddServiceAreaModalStep.DrawOrSelectZipcode);
      setData(defaultState);
    }
  }, [visible]);
  useEffect(() => {
    if (enableGeoFencing && data.serviceAreaType === ServiceAreaType.Polygon) {
      setData({ serviceAreaType: ServiceAreaType.Zipcode });
    }
  }, [enableGeoFencing, data.serviceAreaType]);

  return (
    <Modal
      classNames={{
        base: cn([
          currentStep === AddServiceAreaModalStep.DrawOrSelectZipcode ? 'moe-w-[800px]' : 'moe-w-[600px]',
          'moe-m-auto',
        ]),
        body: 'moe-p-[24px] moe-px-[32px]',
      }}
      title={<div className="moe-flex moe-items-center">Create a service area</div>}
      onClose={onClose}
      isOpen={visible}
      renderActions={() => (
        <AddServiceAreaModalFooter
          currentStep={currentStep}
          onNext={() => {
            verify(data);
            setCurrentStep(AddServiceAreaModalStep.EditNameAndColorCode);
          }}
          onBack={() => {
            setCurrentStep(AddServiceAreaModalStep.DrawOrSelectZipcode);
          }}
          onSave={handleSave}
          onClose={onClose}
        />
      )}
    >
      <div className="moe-h-full">
        <Condition if={currentStep === AddServiceAreaModalStep.DrawOrSelectZipcode}>
          <ServiceAreaTypeBar
            currentType={data.serviceAreaType}
            onChange={(type) => setData({ serviceAreaType: type })}
          />
          <Condition if={data.serviceAreaType === ServiceAreaType.Zipcode}>
            <div className="moe-mb-[24px]">
              <div className="moe-text-[#333] moe-text-[14px] moe-font-[700] moe-leading-[18px] moe-mb-[4px]">
                Zipcode
              </div>
              {enableGeoFencing ? (
                <ServiceAreaZipcodeSelect
                  zipcodes={data.zipcodes}
                  onChange={(zipcodes: string[]) => setData({ zipcodes })}
                />
              ) : (
                <ServiceAreaZipcodeSearchInput
                  zipcode={data.zipcodes}
                  onChange={(zipcodes: string[]) => setData({ zipcodes })}
                />
              )}
            </div>
          </Condition>
        </Condition>
        <div
          className={classNames('moe-w-full moe-h-[400px]', {
            // 隐藏比卸载更好，因为卸载后再加载，会导致地图闪烁，或者无法加载
            'moe-hidden': currentStep !== AddServiceAreaModalStep.DrawOrSelectZipcode,
          })}
        >
          <DrawServiceArea
            mapStyle={{ height: 400 }}
            visible={visible}
            value={data.polygon}
            onChange={(value) => {
              if (isPolygonType) {
                setData({ polygon: value as LatLng[] });
              }
            }}
            serviceAreaType={data.serviceAreaType}
            placeIdList={placeIdList}
            businessId={businessId}
          />
        </div>

        <div
          className={classNames({
            'moe-hidden': currentStep !== AddServiceAreaModalStep.EditNameAndColorCode,
          })}
        >
          <ServiceAreaBasicInfo
            colorCodeMode={ServiceAreaInfoColorCodeMode.Full}
            ref={basicInfoRef}
            visible={visible}
          />
        </div>
      </div>
    </Modal>
  );
});
