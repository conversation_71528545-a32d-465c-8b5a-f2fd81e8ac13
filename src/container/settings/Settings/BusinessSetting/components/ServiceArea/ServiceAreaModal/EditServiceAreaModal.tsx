import { growthBook } from '../../../../../../../utils/growthBook/growthBook';
import { GrowthBookFeatureList } from '../../../../../../../utils/growthBook/growthBook.config';
import { ServiceAreaZipcodeSelect } from './ServiceAreaZipcodeSelect';

import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useRef } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../../../../components/Condition';
import { Modal } from '@moego/ui';
import {
  DEFAULT_SERVICE_AREA_CONFIG,
  ServiceAreaInfoColorCodeMode,
  ServiceAreaType,
  defaultState,
} from '../../../../../../../components/ServiceArea/ServiceArea.options';
import { useGetPlaceIdList } from '../../../../../../../components/ServiceArea/hooks/useGetPlaceIdByZipcode';
import { verify } from '../../../../../../../components/ServiceArea/hooks/useSubmit';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import { updateServiceArea } from '../../../../../../../store/serviceArea/serviceArea.actions';
import { type ServiceAreaModel, serviceAreaMapBox } from '../../../../../../../store/serviceArea/serviceArea.boxes';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { type LatLng } from '../../../../../../../utils/geo';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { DrawServiceArea } from '../DrawServiceArea/DrawServiceArea';
import { ServiceAreaBasicInfo, type ServiceAreaBasicInfoRef, type ServiceAreaInfoProps } from '../ServiceAreaBasicInfo';
import { EditServiceAreaModalFooter } from './EditServiceAreaModalFooter';
import { ServiceAreaTypeBar } from './ServiceAreaTypeBar';
import { ServiceAreaZipcodeSearchInput } from './ServiceAreaZipcodeSearchInput';

export interface EditServiceAreaModalProps {
  visible: boolean;
  onClose: () => void;
  areaId: number;
  businessId: number;
}

export const EditServiceAreaModal = memo((props: EditServiceAreaModalProps) => {
  const dispatch = useDispatch();
  const basicInfoRef = useRef<ServiceAreaBasicInfoRef>(null);
  const { onClose, visible, areaId, businessId } = props;
  const [serviceArea] = useSelector(serviceAreaMapBox.mustGetItem(areaId));

  const [data, setData] = useSetState(defaultState);
  const [basicInfo, setBasicInfo] = useSetState<ServiceAreaInfoProps>();

  const isPolygonType = data.serviceAreaType === ServiceAreaType.Polygon;
  const placeIdList = useGetPlaceIdList(data.zipcodes);
  const enableGeoFencing = growthBook.isOn(GrowthBookFeatureList.EnableGeoFencing);

  useEffect(() => {
    if (isNormal(serviceArea.areaId)) {
      setData(serviceArea.toJS() as ServiceAreaModel);
      setBasicInfo({
        areaName: serviceArea.areaName,
        colorCode: serviceArea.colorCode || DEFAULT_SERVICE_AREA_CONFIG.drawColor,
      });
    }
  }, [serviceArea]);

  useEffect(() => {
    if (enableGeoFencing && data.serviceAreaType === ServiceAreaType.Polygon) {
      setData({ serviceAreaType: ServiceAreaType.Zipcode });
    }
  }, [enableGeoFencing, data.serviceAreaType]);

  const handleSave = useLatestCallback(async () => {
    try {
      verify(data);
      const basicInfo = await basicInfoRef.current?.getData();

      if (basicInfo) {
        const result = await dispatch(
          updateServiceArea(
            {
              ...data, // 这里的data还包含初始basicInfo,所以放前面
              ...basicInfo,
              areaId,
            },
            businessId,
          ),
        );
        if (result.success) {
          onClose();
          toastApi.success('Update service area successfully');
        }
      }
    } catch {
      toastApi.error('Failed to update service area');
    }
  });

  return (
    <Modal
      title="Edit service area"
      onClose={onClose}
      isOpen={visible}
      classNames={{
        base: 'moe-w-[800px] moe-m-auto',
        body: 'moe-p-[24px] moe-px-[32px]',
      }}
      renderActions={() => <EditServiceAreaModalFooter onCancel={onClose} onSave={handleSave} />}
    >
      <div className="moe-h-full">
        <Condition if={basicInfo}>
          <ServiceAreaBasicInfo
            colorCodeMode={ServiceAreaInfoColorCodeMode.Mini}
            ref={basicInfoRef}
            visible={visible}
            basicInfo={basicInfo!}
            onChange={(v) => setBasicInfo(v)}
          />
        </Condition>
        <div className="moe-w-[calc(100%+64px)] moe-h-[1px] moe-bg-[#E6E6E6] moe-mb-[24px] moe-mr-[-32px] moe-ml-[-32px] moe-mt-[24px]"></div>
        <ServiceAreaTypeBar
          currentType={data.serviceAreaType}
          onChange={(type) => setData({ serviceAreaType: type })}
        />
        <Condition if={data.serviceAreaType === ServiceAreaType.Zipcode}>
          <div className="moe-mb-[24px]">
            <div className="moe-text-[#333] moe-text-[14px] moe-font-[700] moe-leading-[18px] moe-mb-[4px]">
              Zipcode
            </div>
            {enableGeoFencing ? (
              <ServiceAreaZipcodeSelect
                zipcodes={data.zipcodes}
                onChange={(zipcodes: string[]) => setData({ zipcodes })}
              />
            ) : (
              <ServiceAreaZipcodeSearchInput
                zipcode={data.zipcodes}
                onChange={(zipcodes: string[]) => setData({ zipcodes })}
              />
            )}
          </div>
        </Condition>
        <div className="moe-w-full moe-h-[400px]">
          <DrawServiceArea
            key="edit-service-area"
            mapStyle={{ height: 400 }}
            visible={visible}
            value={data.polygon}
            onChange={(value) => {
              if (isPolygonType) {
                setData({ polygon: value as LatLng[] });
              }
            }}
            colorCode={basicInfo.colorCode}
            serviceAreaType={data.serviceAreaType}
            placeIdList={placeIdList}
            areaId={areaId}
            businessId={businessId}
          />
        </div>
      </div>
    </Modal>
  );
});
