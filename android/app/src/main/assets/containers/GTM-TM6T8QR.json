{"fingerprint": "MQ$0", "resource": {"version": "1", "macros": [{"function": "__ai", "instance_name": "App ID"}, {"function": "__an", "instance_name": "App Name"}, {"function": "__av", "instance_name": "App Version Code"}, {"function": "__avn", "instance_name": "App Version Name"}, {"function": "__e", "instance_name": "Event Name"}], "tags": [], "predicates": [], "rules": []}, "runtime": [[50, "__ai_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationId", [7]]]], [50, "__ai", [46, "data"], [36, ["__ai_main", [15, "data"]]]], [50, "__an_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationName", [7]]]], [50, "__an", [46, "data"], [36, ["__an_main", [15, "data"]]]], [50, "__av_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationVersion", [7]]]], [50, "__av", [46, "data"], [36, ["__av_main", [15, "data"]]]], [50, "__avn_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationVersionName", [7]]]], [50, "__avn", [46, "data"], [36, ["__avn_main", [15, "data"]]]], [50, "__e_main", [46], [41, "a", "b"], [3, "a", [2, [17, [15, "gtmUtils"], "mobile"], "event", [7]]], [22, [1, [1, [15, "a"], [18, [17, [15, "a"], "length"], 0]], [12, [16, [15, "a"], 0], "_"]], [46, [3, "b", [8, "_f", "first_open", "_v", "first_visit", "_iap", "in_app_purchase", "_e", "user_engagement", "_s", "session_start", "_ssr", "session_start_with_rollout", "_au", "app_update", "_ui", "app_remove", "_ab", "app_background", "_ou", "os_update", "_cd", "app_clear_data", "_ae", "app_exception", "_nf", "notification_foreground", "_nr", "notification_receive", "_no", "notification_open", "_nd", "notification_dismiss", "_cmp", "firebase_campaign", "_cmpx", "invalid_campaign", "_vs", "screen_view", "_ar", "ad_reward", "_asr", "app_store_refund", "_assc", "app_store_subscription_convert", "_assr", "app_store_subscription_renew", "_asse", "app_store_subscription_cancel"]], [3, "a", [30, [16, [15, "b"], [15, "a"]], [15, "a"]]]]], [36, [15, "a"]]], [50, "__e", [46, "data"], [36, ["__e_main", [15, "data"]]]], [50, "main", [46, "a"], [43, [17, [15, "a"], "common"], "tableToMap", [15, "tableToMap"]], [43, [17, [15, "a"], "common"], "stringify", [15, "stringify"]], [43, [17, [15, "a"], "common"], "copy", [15, "copy"]], [43, [17, [15, "a"], "common"], "split", [15, "split"]]], [50, "tableToMap", [46, "a", "b", "c"], [41, "d", "e", "f"], [3, "d", [8]], [3, "e", false], [3, "f", 0], [42, [1, [15, "a"], [23, [15, "f"], [17, [15, "a"], "length"]]], [33, [15, "f"], [3, "f", [0, [15, "f"], 1]]], false, [46, [22, [1, [1, [16, [15, "a"], [15, "f"]], [2, [16, [15, "a"], [15, "f"]], "hasOwnProperty", [7, [15, "b"]]]], [2, [16, [15, "a"], [15, "f"]], "hasOwnProperty", [7, [15, "c"]]]], [46, [43, [15, "d"], [16, [16, [15, "a"], [15, "f"]], [15, "b"]], [16, [16, [15, "a"], [15, "f"]], [15, "c"]]], [3, "e", true]]]]], [36, [39, [15, "e"], [15, "d"], [45]]]], [50, "stringify", [46, "a"], [41, "b", "c", "d", "e"], [22, [20, [15, "a"], [45]], [46, [36, "null"]]], [22, [20, [15, "a"], [44]], [46, [36, [44]]]], [22, [30, [12, [40, [15, "a"]], "number"], [12, [40, [15, "a"]], "boolean"]], [46, [36, [2, [15, "a"], "toString", [7]]]]], [22, [12, [40, [15, "a"]], "string"], [46, [36, [0, [0, "\"", [2, ["split", [15, "a"], "\""], "join", [7, "\\\""]]], "\""]]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "a"]]], [46, [3, "b", [7]], [3, "c", 0], [42, [23, [15, "c"], [17, [15, "a"], "length"]], [33, [15, "c"], [3, "c", [0, [15, "c"], 1]]], false, [46, [3, "d", ["stringify", [16, [15, "a"], [15, "c"]]]], [22, [12, [15, "d"], [44]], [46, [2, [15, "b"], "push", [7, "null"]]], [46, [2, [15, "b"], "push", [7, [15, "d"]]]]]]], [36, [0, [0, "[", [2, [15, "b"], "join", [7, ","]]], "]"]]]], [22, [12, [40, [15, "a"]], "object"], [46, [3, "b", [7]], [47, "e", [15, "a"], [46, [3, "d", ["stringify", [16, [15, "a"], [15, "e"]]]], [22, [29, [15, "d"], [44]], [46, [2, [15, "b"], "push", [7, [0, [0, [0, "\"", [15, "e"]], "\":"], [15, "d"]]]]]]]], [36, [0, [0, "{", [2, [15, "b"], "join", [7, ","]]], "}"]]]], [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", "Attempting to stringify unknown type!"]], [36, [44]]], [50, "split", [46, "a", "b"], [41, "c", "d", "e", "f"], [3, "c", [7]], [22, [20, [15, "b"], ""], [46, [3, "d", [17, [15, "a"], "length"]], [3, "e", 0], [42, [23, [15, "e"], [15, "d"]], [33, [15, "e"], [3, "e", [0, [15, "e"], 1]]], false, [46, [2, [15, "c"], "push", [7, [16, [15, "a"], [15, "e"]]]]]], [36, [15, "c"]]]], [42, [1, [15, "a"], [19, [2, [15, "a"], "indexOf", [7, [15, "b"]]], 0]], [46], false, [46, [3, "f", [2, [15, "a"], "indexOf", [7, [15, "b"]]]], [22, [12, [15, "f"], 0], [46, [2, [15, "c"], "push", [7, ""]]], [46, [2, [15, "c"], "push", [7, [2, [15, "a"], "substring", [7, 0, [15, "f"]]]]]]], [3, "a", [2, [15, "a"], "substring", [7, [0, [15, "f"], [17, [15, "b"], "length"]]]]]]], [2, [15, "c"], "push", [7, [15, "a"]]], [36, [15, "c"]]], [50, "copy", [46, "a", "b"], [41, "c", "d"], [3, "b", [30, [15, "b"], [39, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "a"]]], [7], [8]]]], [47, "c", [15, "a"], [46, [3, "d", [16, [15, "a"], [15, "c"]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "d"]]], [46, [22, [28, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [16, [15, "b"], [15, "c"]]]]], [46, [43, [15, "b"], [15, "c"], [7]]]], [43, [15, "b"], [15, "c"], ["copy", [15, "d"], [16, [15, "b"], [15, "c"]]]]], [46, [22, [1, [29, [15, "d"], [45]], [12, [40, [15, "d"]], "object"]], [46, [22, [29, [40, [16, [15, "b"], [15, "c"]]], "object"], [46, [43, [15, "b"], [15, "c"], [8]]]], [43, [15, "b"], [15, "c"], ["copy", [15, "d"], [16, [15, "b"], [15, "c"]]]]], [46, [43, [15, "b"], [15, "c"], [15, "d"]]]]]]]], [36, [15, "b"]]]]}