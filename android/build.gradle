// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = findProperty('android.buildToolsVersion') ?: '34.0.0'
        // manually change minSdkVersion to 26（for stripe-terminal-react-native） https://github.com/stripe/stripe-terminal-react-native?tab=readme-ov-file#requirements
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '26')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '34')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '34')
        kotlinVersion = findProperty('android.kotlinVersion') ?: '1.9.23'

        ndkVersion = "26.1.10909125"

        // ------------------------- <PERSON><PERSON><PERSON> ADDED -------------------------
        multiDexEnabled = true
        readerSdkVersion = "1.7.7"
        sqipVersion = "1.6.6"
        // ------------------------- MANUAL ADDED -------------------------
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
          classpath('com.google.gms:google-services:4.4.1')
          classpath('com.android.tools.build:gradle')
          classpath('com.facebook.react:react-native-gradle-plugin')
          classpath('org.jetbrains.kotlin:kotlin-gradle-plugin')
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url(new File(['node', '--print', "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim(), '../android'))
        }
        maven {
            // Android JSC is installed from npm
            url(new File(['node', '--print', "require.resolve('jsc-android/package.json', { paths: [require.resolve('react-native/package.json')] })"].execute(null, rootDir).text.trim(), '../dist'))
        }

        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }

        // ------------------------- MANUAL ADDED -------------------------
        // https://github.com/square/in-app-payments-android-quickstart/issues/30
        maven {
          url(new File("$rootDir/app/square"))
        }
        maven {
          url 'https://sdk.squareup.com/public/android'
        }

        maven {
          url "https://sdk.squareup.com/android"
          credentials {
            username SQUARE_READER_SDK_APPLICATION_ID
            password SQUARE_READER_SDK_REPOSITORY_PASSWORD
          }
        }
        // ------------------------- MANUAL ADDED -------------------------
    }
}
